<script setup lang="ts">
import LiveCard from './components/LiveCard.vue'
import LiveGrid from './components/LiveGrid.vue'
import { cn } from '@/lib/utils'
import type { DateRange } from 'reka-ui'
import { RangeCalendar } from '@/components/ui/range-calendar'
import { ScrollArea } from '@/components/ui/scroll-area'

import {
  CreatedLiveReqVO,
  DeptAndUserMixedVO,
  LiveApi,
  LiveSortEnum,
  LiveStatusEnum,
  PageResultRoomListVO,
  RoomListVO
} from '@/api/live/stream'
import { useUserStore } from '@/store/modules/user'
import { useCache } from '@/hooks/web/useCache'
import dayjs from 'dayjs'
import { useLiveMenu } from './hooks/useLiveMenu'
import { LiveMenuEnum } from './types'
import RecommendedLives from './components/RecommendedLives.vue'

import addDays from 'date-fns/addDays'
import addMonths from 'date-fns/addMonths'
import addHours from 'date-fns/addHours'
import format from 'date-fns/format'
import nextSaturday from 'date-fns/nextSaturday'

import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date'
import { type Ref, ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import ScheduleDialog from './components/ScheduleDialog.vue'
import {
  CalendarIcon,
  Trash,
  Search,
  BellRing,
  Play,
  X,
  ChevronDown,
  Filter
} from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
import { ContainerScroll, ContainerWrapper } from '@/components/ContainerWrap'
import { Label } from '@/components/ui/label'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { SmartPagination } from '@/components/SmartPagination'
import { Button } from '@/components/ui/button'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'

const {
  // explores,
  collectors,
  activeLiveMenu
} = useLiveMenu()

const df = new DateFormatter('en-US', {
  dateStyle: 'medium'
})

const name = ref('')

defineOptions({ name: 'LiveCenter' })

// Sort options
const sortBy = ref(1)
const sortOptions = [
  { value: 1, label: 'Newest' },
  { value: 2, label: 'Popular' }
]

// Computed properties
const hasActiveFilters = computed(() => {
  return (
    selectedStatus.value !== 'all' || name.value !== '' || calendar.value.start !== undefined
  )
})

interface SubMenu {
  name: string
  param: string
  icon: string
  value: string
}

interface DepartmentAndUserData extends DeptAndUserMixedVO {
  isChecked: boolean
}

// 使用缓存
const { wsCache } = useCache()
// 使用自定义菜单

const { push, replace } = useRouter()
const route = useRoute()
const userStore = useUserStore()
const currentUser = computed(() => userStore.user)

// 今天的时间
const today = new Date()
const calendar = ref({
  start: undefined,
  end: undefined
}) as Ref<DateRange>

// 状态选择 - 改为 ToggleGroup 格式
const selectedStatus = ref<string>('all')
const statusTabs = [
  { key: 'all', label: 'All' },
  { key: 'draft', label: 'Draft' },
  { key: 'upcoming', label: 'Upcoming' },
  { key: 'living', label: 'Living' }
]

// 保持原有的状态参数列表用于API调用
const statusParamList = ref<SubMenu[]>([
  {
    name: 'All',
    param: 'all',
    icon: '',
    value: ''
  },
  {
    name: 'Draft',
    param: 'draft',
    icon: '',
    value: '10'
  },
  {
    name: 'Scheduled',
    param: 'upcoming',
    icon: '',
    value: '20'
  },
  {
    name: 'In Progress',
    param: 'living',
    icon: '',
    value: '50'
  }
])

// 兼容原有的 activeStatusParam
const activeStatusParam = computed(() => {
  return selectedStatus.value === 'all' ? [] : [selectedStatus.value]
})

// Additional computed properties for filtering

// 拼接参数
const calcParams = () => {
  const type = `type=${activeLiveMenu.value?.type}`
  const time = `${calendar.value.start && calendar.value.end ? '&time=' + [format(calendar.value.start, 'yyyy-MM-dd'), format(calendar.value.end, 'yyyy-MM-dd')].join('_') : ''}`
  const status = `&status=${selectedStatus.value === 'all' ? 'all' : selectedStatus.value}`
  const search = `${queryParams.name ? '&search=' + queryParams.name : ''}`
  const sort = `&sort=${queryParams.sortRule}`

  return { type, time, status, search, sort }
}
// 直播开始时间范围切换
const handleChangeTimeRange = () => {
  const { type, time, status, search, sort } = calcParams()
  replace(`/live/center?${type}${time}${status}${search}${sort}`)
}

// 直播状态切换 - 更新为 ToggleGroup 处理方式
const handleChangeStatus = (status: string) => {
  if (!status) return // ToggleGroup can return undefined when deselecting

  selectedStatus.value = status
  const { type, time, status: statusParam, search, sort } = calcParams()
  replace(`/live/center?${type}${time}${statusParam}${search}${sort}`)
}

// 直播列表排序
const handleChangeSort = (sortRule: number) => {
  queryParams.sortRule = sortRule
  const { type, time, status, search, sort } = calcParams()
  replace(`/live/center?${type}${time}${status}${search}${sort}`)
}

// 页面是否初始化，如初始化后的加载，不显示骨架
const liveLoading = ref(false)
const message = useMessage()
const speakers = ref<DepartmentAndUserData[]>([])
const lives = ref<RoomListVO[]>([])
const total = ref(0)
const queryParams = reactive<CreatedLiveReqVO>({
  pageNo: 1,
  pageSize: 10,
  name: '',
  statusList: [],
  startTime: [],
  sortRule: LiveSortEnum.NEWEST
})

// 分页器获取页面变更
const handleCurrentChange = async (newPage: number) => {
  queryParams.pageNo = newPage
  await getLiveList()
}

const getRemainingTime = (startTime: number): number => {
  const now = dayjs() // 当前时间
  const start = dayjs(startTime) // 直播开始时间
  return start.diff(now) // 返回剩余时间（毫秒）
}

const getLiveList = async () => {
  try {
    liveLoading.value = true
    let data: PageResultRoomListVO
    switch (activeLiveMenu.value?.type) {
      case LiveMenuEnum.STREAM_CENTRAL:
        data = await LiveApi.getVisibleRoom(queryParams)
        break
      case LiveMenuEnum.NEW_RELEASES:
        const start = new Date()
        const end = new Date()
        const currentDay = start.getDay()
        const diff = start.getDate() - currentDay + (currentDay === 0 ? -6 : 1) // 调整为本周一
        start.setDate(diff)
        start.setHours(0, 0, 0, 0)
        end.setDate(start.getDate() + 6)
        end.setHours(23, 59, 59, 999)

        queryParams.startTime = [
          format(start, 'yyyy-MM-dd 00:00:00'),
          format(end, 'yyyy-MM-dd 23:59:59')
        ]
        queryParams.sortRule = LiveSortEnum.NEWEST
        data = await LiveApi.getVisibleRoom(queryParams)
        break
      case LiveMenuEnum.TOP_STREAMS:
        queryParams.statusList = [LiveStatusEnum.LIVING, LiveStatusEnum.UPCOMING]
        queryParams.sortRule = LiveSortEnum.HOTTEST
        data = await LiveApi.getVisibleRoom(queryParams)
        break
      case LiveMenuEnum.UPCOMING_SCHEDULE:
        queryParams.statusList = [LiveStatusEnum.UPCOMING]
        data = await LiveApi.getJoinedRoomPage(queryParams)
        break
      case LiveMenuEnum.HISTORY:
        queryParams.statusList = [LiveStatusEnum.OVER]
        data = await LiveApi.getJoinedRoomPage(queryParams)
        break
      case LiveMenuEnum.MY_STREAMS:
        data = await LiveApi.getCreatedRoomPage(queryParams)
        break
      case LiveMenuEnum.FAVORITES:
        data = await LiveApi.getFavoriteList(queryParams)
        break
      case LiveMenuEnum.REPLAYS:
        data = await LiveApi.getRecordingPage(queryParams)
        break
      default:
        data = { list: [], total: 0 }
        break
    }
    lives.value = data?.list || []
    lives.value = lives.value.map((item: RoomListVO) => {
      return {
        ...item,
        isShowCountDown: true,
        remainingTime: getRemainingTime(Number(item.startTime))
      }
    })
    total.value = data?.total || 0
  } catch {
    liveLoading.value = false
  } finally {
    liveLoading.value = false
  }
}

const getParams = () => {
  const timeParam = useRouteQuery('time', '', { transform: String }).value
  if (timeParam !== '') {
    const { start, end } = calcTimeRange(timeParam)

    if (start && end) {
      calendar.value = {
        start: new CalendarDate(start.getFullYear(), start.getMonth() + 1, start.getDate()),
        end: new CalendarDate(end.getFullYear(), end.getMonth() + 1, end.getDate())
      }

      queryParams.startTime = [
        format(start, 'yyyy-MM-dd 00:00:00'),
        format(end, 'yyyy-MM-dd 23:59:59')
      ]
    }
  }

  const statusParam = useRouteQuery('status', 'all', { transform: String }).value
  selectedStatus.value = statusParam.split('-')[0] || 'all'

  queryParams.statusList = statusParamList.value
    .filter((_item) =>
      activeStatusParam.value.filter((_status) => _status !== 'all').includes(_item.param)
    )
    .map((_item) => _item.value)

  queryParams.name = useRouteQuery('search', '', { transform: String }).value
  name.value = queryParams.name
  queryParams.sortRule = sortBy.value = useRouteQuery('sort', 1, { transform: Number }).value
}

const calcTimeRange = (option: string) => {
  let start = new Date()
  let end = new Date()

  switch (option) {
    case 'today':
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      break
    case 'tomorrow':
      start.setDate(start.getDate() + 1)
      start.setHours(0, 0, 0, 0)
      end.setDate(end.getDate() + 1)
      end.setHours(23, 59, 59, 999)
      break
    case 'thisWeek':
      // 计算本周一的日期
      const currentDay = start.getDay()
      const diff = start.getDate() - currentDay + (currentDay === 0 ? -6 : 1) // 调整为本周一
      start.setDate(diff)
      start.setHours(0, 0, 0, 0)
      end.setDate(start.getDate() + 6)
      end.setHours(23, 59, 59, 999)
      break

    case 'nextWeek':
      const currentDay1 = start.getDay()
      const daysUntilNextMonday = 7 - currentDay1 + 1 // 计算到下周一开始的天数
      start.setDate(start.getDate() + daysUntilNextMonday)
      start.setHours(0, 0, 0, 0)

      // 基于 start 计算 end（start +6天）
      const end1 = new Date(start.getTime())
      end1.setDate(end1.getDate() + 6)
      end1.setHours(23, 59, 59, 999)

      // 替换原来的 end 变量
      end = end1 // 确保使用新计算的 end
      break

    case 'thisMonth':
      const current = new Date()
      const year = current.getFullYear()
      const month = current.getMonth()
      // 重新初始化 start 和 end
      start = new Date(year, month, 1)
      start.setHours(0, 0, 0, 0)
      end = new Date(year, month + 1, 0)
      end.setHours(23, 59, 59, 999)
      break

    case 'nextMonth':
      const currentNext = new Date()
      const yearNext = currentNext.getFullYear()
      const monthNext = currentNext.getMonth()

      start = new Date(yearNext, monthNext + 1, 1)
      start.setHours(0, 0, 0, 0)

      end = new Date(yearNext, monthNext + 2, 0)
      end.setHours(23, 59, 59, 999)
      break

    case 'clear':
      start = null
      end = null
      break
    default:
      const regex = /^(\d{4}-\d{2}-\d{2})_(\d{4}-\d{2}-\d{2})$/
      if (regex.test(option)) {
        const [startDate, endDate] = option.split('_')
        start.setTime(Date.parse(startDate))
        end.setTime(Date.parse(endDate))
      } else {
        message.warning('Invalid time setting.')
        return { start: null, end: null }
      }
      break
  }
  return { start, end }
}

const handleQuickOption = (option: string) => {
  const { start, end } = calcTimeRange(option)

  if (start && end) {
    calendar.value = {
      start: new CalendarDate(start.getFullYear(), start.getMonth() + 1, start.getDate()),
      end: new CalendarDate(end.getFullYear(), end.getMonth() + 1, end.getDate())
    }
  } else {
    calendar.value = {
      start: undefined,
      end: undefined
    }
  }
  handleChangeTimeRange()
}

// 直播名称搜索
const handleSearch = () => {

  // 创建新的查询参数对象，合并当前查询参数和新的 search 参数
  const queryParams = {
    ...route.query,
    search: name.value
  }

  // 使用合并后的查询参数进行跳转
  replace({ path: '/live/center', query: queryParams })
}

// Sort change handler
const handleSortChange = (value: string) => {
  sortBy.value = value
  // Apply sorting logic based on value
  switch (value) {
    case 'newest':
      queryParams.sortRule = LiveSortEnum.NEWEST
      break
    case 'hottest':
      queryParams.sortRule = LiveSortEnum.HOTTEST
      break
  }
  getLiveList()
}

// Live click handler
const handleLiveClick = (live: any) => {
  push(`/live/detail?id=${live.id}`)
}

// Live update handler - 更新列表中的直播数据
const handleLiveUpdate = (updatedLive: any) => {
  const index = lives.value.findIndex((live) => live.id === updatedLive.id)
  if (index !== -1) {
    lives.value[index] = { ...lives.value[index], ...updatedLive }
  }
}

// Status toggle handler - 更新为新的处理方式
const handleStatusToggle = (value: string) => {
  handleChangeStatus(value)
}

// Clear all filters
const clearAllFilters = () => {
  name.value = ''
  selectedStatus.value = 'all'
  calendar.value = {
    start: undefined,
    end: undefined
  }
  // Reset to default state and reload
  replace({ path: '/live/center' })
  getLiveList()
}

onMounted(async () => {
  getParams()
  await getLiveList()
  speakers.value.push({
    isChecked: true,
    id: currentUser.value.id,
    name: currentUser.value.nickname,
    type: 2,
    avatar: currentUser.value.avatar
  })
  const errorsParam = route.query.errors as string
  if (errorsParam) {
    const errors = JSON.parse(decodeURIComponent(errorsParam))
    message.notifyError(errors[0].message)
    console.log(errors[0].message)
  }
})
</script>

<template>
  <ContainerWrapper :autoCollapse="true">
    <!-- Left Navigation -->
    <template #nav>
      <Sidebar :collapsible="'none'" class="border-r-0 w-full bg-white">
        <SidebarHeader class="border-b-0">
          <div class="flex items-center gap-3 px-2 py-3">
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <Play class="size-4" />
            </div>
            <div class="flex flex-col gap-0 leading-none">
              <span class="font-semibold text-sidebar-foreground">Live Stream</span>
              <span class="text-xs text-sidebar-foreground/60">Learning Platform</span>
            </div>
          </div>
        </SidebarHeader>

        <SidebarContent class="px-0 overflow-y-auto">
          <!-- Live Stream Categories -->
          <SidebarGroup>
            <SidebarGroupLabel>Categories</SidebarGroupLabel>
            <SidebarGroupContent class="px-0">
              <SidebarMenu>
                <SidebarMenuItem v-for="menu in collectors" :key="menu.type">
                  <SidebarMenuButton
                    @click="$router.push(menu.href)"
                    :is-active="activeLiveMenu?.type === menu.type"
                  >
                    <component :is="menu.icon" class="size-4" />
                    {{ menu.name }}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </template>
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="w-full h-full px-4 flex items-center gap-2">
            <SuperSearch
              v-if="activeLiveMenu && activeLiveMenu?.param.search"
              v-model="name"
              :loading="liveLoading"
              @search="handleSearch"
              clearable
              @clear="handleSearch"
              @keyup="handleSearch"
              placeholder="Search for content..."
            />

            <!-- 开始时间 -->
            <template v-if="activeLiveMenu?.param.startTime">
              <Popover>
                <PopoverTrigger as-child>
                  <Button
                    variant="ghost"
                    :class="
                      cn(
                        'me-auto rounded-full',
                        !calendar && 'text-muted-foreground',
                        calendar.start ? '' : 'size-8'
                      )
                    "
                  >
                    <CalendarIcon class="size-4 shrink-0" />
                    <template v-if="calendar.start">
                      <template v-if="calendar.end">
                        {{ df.format(calendar.start.toDate(getLocalTimeZone())) }} -
                        {{ df.format(calendar.end.toDate(getLocalTimeZone())) }}
                      </template>

                      <template v-else>
                        {{ df.format(calendar.start.toDate(getLocalTimeZone())) }}
                      </template>
                    </template>
                    <!--            <template v-else> Pick a date </template>-->
                  </Button>
                </PopoverTrigger>

                <PopoverContent class="flex w-auto p-0">
                  <div class="flex flex-col gap-2 border-r px-2 py-4">
                    <div class="px-4 text-sm font-medium text-muted-foreground">
                      Quick Options
                    </div>
                    <div class="grid gap-1">
                      <Button
                        variant="ghost"
                        class="justify-start font-normal"
                        @click="handleQuickOption('today')"
                      >
                        Today
                      </Button>
                      <Button
                        variant="ghost"
                        class="justify-start font-normal"
                        @click="handleQuickOption('tomorrow')"
                      >
                        Tomorrow
                      </Button>
                      <Button
                        variant="ghost"
                        class="justify-start font-normal"
                        @click="handleQuickOption('thisWeek')"
                      >
                        This Week
                      </Button>
                      <Button
                        variant="ghost"
                        class="justify-start font-normal"
                        @click="handleQuickOption('nextWeek')"
                      >
                        Next Week
                      </Button>
                      <Button
                        variant="ghost"
                        class="justify-start font-normal"
                        @click="handleQuickOption('thisMonth')"
                      >
                        This Month
                      </Button>
                      <Button
                        variant="ghost"
                        class="justify-start font-normal"
                        @click="handleQuickOption('nextMonth')"
                      >
                        Next Month
                      </Button>
                      <!--清空日期控件-->
                      <Button
                        variant="outline"
                        class="justify-start font-normal"
                        @click="handleQuickOption('clear')"
                      >
                        <Trash class="size-4 shrink-0" />
                        Clear
                      </Button>
                    </div>
                  </div>
                  <div class="p-2">
                    <RangeCalendar
                      v-model="calendar"
                      initial-focus
                      :number-of-months="2"
                      @update:model-value="handleChangeTimeRange"
                    />
                  </div>
                </PopoverContent>
              </Popover>
            </template>

            <!-- 状态 - 使用新的 ToggleGroup 格式 -->
            <template v-if="activeLiveMenu?.param.status">
              <ToggleGroup
                v-model="selectedStatus"
                type="single"
                size="sm"
                class="space-x-1"
                @update:model-value="handleChangeStatus"
              >
                <ToggleGroupItem
                  v-for="tab in statusTabs.filter((_tab) => !([LiveMenuEnum.STREAM_CENTRAL, LiveMenuEnum.FAVORITES].includes(activeLiveMenu?.type!) && _tab.key === 'draft') )"
                  :key="tab.key"
                  :value="tab.key"
                  class="px-3"
                >
                  {{ tab.label }}
                </ToggleGroupItem>
              </ToggleGroup>
            </template>

            <!-- Spacer between ToggleGroup and Create Live button -->
            <div class="flex-1"></div>

            <!-- Create Live Button -->
            <ScheduleDialog v-if="activeLiveMenu && activeLiveMenu?.param.search" />
          </div>
        </template>
        <template #statistics>
          <div class="p-4 flex items-center justify-between">
            <div class="text-sm text-muted-foreground"> Found {{ total }} live streams </div>
            <div class="flex items-center gap-3">
              <Label class="text-sm font-medium text-muted-foreground whitespace-nowrap"
                >Sort by:</Label
              >
              <div class="flex flex-wrap gap-1">
                <button
                  v-for="option in sortOptions"
                  :key="option.value"
                  @click="handleChangeSort(option.value)"
                  :class="[
                    'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-colors border',
                    sortBy === option.value
                      ? 'bg-primary text-primary-foreground border-primary shadow-sm'
                      : 'bg-background text-muted-foreground border-border hover:bg-muted hover:text-foreground hover:border-muted-foreground'
                  ]"
                >
                  {{ option.label }}
                </button>
              </div>
            </div>
          </div>
        </template>

        <!-- Scrollable Content: Live Grid -->
        <div class="flex-1 overflow-hidden">
          <ScrollArea class="h-full">
            <div class="px-4 pt-0 pb-4 h-full">
              <LiveGrid
                :lives="lives"
                :loading="liveLoading"
                empty-title="No live streams found"
                empty-description="Try adjusting your search or filter criteria"
                :show-clear-button="hasActiveFilters"
                @live-click="handleLiveClick"
                @clear-filters="clearAllFilters"
                @live-update="handleLiveUpdate"
              />
            </div>
          </ScrollArea>
        </div>

        <template #footer v-if="total > 0">
          <div class="px-4 flex items-center w-full h-full">
            <SmartPagination
              class="w-full"
              :total="total"
              :current-page="queryParams.pageNo"
              :page-size="queryParams.pageSize"
              @current-change="handleCurrentChange"
            />
          </div>
        </template>
      </ContainerScroll>

      <!--      <div class="h-full flex flex-col">-->
      <!--        <div class="flex items-center px-4 py-2 h-[52px]">-->
      <!--          <SuperSearch v-if="activeLiveMenu && activeLiveMenu?.param.search"-->
      <!--                       v-model="name"-->
      <!--                       :loading="liveLoading"-->
      <!--                       @search="handleSearch"-->
      <!--                       @keyup="handleSearch"-->
      <!--                       round-->
      <!--                       placeholder="Search for content..."-->
      <!--          />-->

      <!--          &lt;!&ndash; 开始时间 &ndash;&gt;-->
      <!--          <template v-if="activeLiveMenu?.param.startTime">-->
      <!--            <Popover>-->
      <!--              <PopoverTrigger as-child>-->
      <!--                <Button-->
      <!--                  variant="ghost"-->
      <!--                  :class="-->
      <!--              cn('me-auto rounded-full', !calendar && 'text-muted-foreground', calendar.start ? '' : 'size-8')-->
      <!--            "-->
      <!--                >-->
      <!--                  <CalendarIcon class="size-4 shrink-0" />-->
      <!--                  <template v-if="calendar.start">-->
      <!--                    <template v-if="calendar.end">-->
      <!--                      {{ df.format(calendar.start.toDate(getLocalTimeZone())) }} - -->
      <!--                      {{ df.format(calendar.end.toDate(getLocalTimeZone())) }}-->
      <!--                    </template>-->

      <!--                    <template v-else>-->
      <!--                      {{ df.format(calendar.start.toDate(getLocalTimeZone())) }}-->
      <!--                    </template>-->
      <!--                  </template>-->
      <!--                  &lt;!&ndash;            <template v-else> Pick a date </template>&ndash;&gt;-->
      <!--                </Button>-->
      <!--              </PopoverTrigger>-->

      <!--              <PopoverContent class="flex w-auto p-0">-->
      <!--                <div class="flex flex-col gap-2 border-r px-2 py-4">-->
      <!--                  <div class="px-4 text-sm font-medium text-muted-foreground"> Quick Options </div>-->
      <!--                  <div class="grid gap-1">-->
      <!--                    <Button-->
      <!--                      variant="ghost"-->
      <!--                      class="justify-start font-normal"-->
      <!--                      @click="handleQuickOption('today')"-->
      <!--                    >-->
      <!--                      Today-->
      <!--                    </Button>-->
      <!--                    <Button-->
      <!--                      variant="ghost"-->
      <!--                      class="justify-start font-normal"-->
      <!--                      @click="handleQuickOption('tomorrow')"-->
      <!--                    >-->
      <!--                      Tomorrow-->
      <!--                    </Button>-->
      <!--                    <Button-->
      <!--                      variant="ghost"-->
      <!--                      class="justify-start font-normal"-->
      <!--                      @click="handleQuickOption('thisWeek')"-->
      <!--                    >-->
      <!--                      This Week-->
      <!--                    </Button>-->
      <!--                    <Button-->
      <!--                      variant="ghost"-->
      <!--                      class="justify-start font-normal"-->
      <!--                      @click="handleQuickOption('nextWeek')"-->
      <!--                    >-->
      <!--                      Next Week-->
      <!--                    </Button>-->
      <!--                    <Button-->
      <!--                      variant="ghost"-->
      <!--                      class="justify-start font-normal"-->
      <!--                      @click="handleQuickOption('thisMonth')"-->
      <!--                    >-->
      <!--                      This Month-->
      <!--                    </Button>-->
      <!--                    <Button-->
      <!--                      variant="ghost"-->
      <!--                      class="justify-start font-normal"-->
      <!--                      @click="handleQuickOption('nextMonth')"-->
      <!--                    >-->
      <!--                      Next Month-->
      <!--                    </Button>-->
      <!--                    &lt;!&ndash;清空日期控件&ndash;&gt;-->
      <!--                    <Button-->
      <!--                      variant="outline"-->
      <!--                      class="justify-start font-normal"-->
      <!--                      @click="handleQuickOption('clear')"-->
      <!--                    >-->
      <!--                      <Trash class="size-4 shrink-0"/>-->
      <!--                      Clear-->
      <!--                    </Button>-->
      <!--                  </div>-->
      <!--                </div>-->
      <!--                <div class="p-2">-->
      <!--                  <RangeCalendar-->
      <!--                    v-model="calendar"-->
      <!--                    initial-focus-->
      <!--                    :number-of-months="2"-->
      <!--                    @update:model-value="handleChangeTimeRange"-->
      <!--                  />-->
      <!--                </div>-->
      <!--              </PopoverContent>-->
      <!--            </Popover>-->
      <!--          </template>-->

      <!--          &lt;!&ndash; 状态 &ndash;&gt;-->
      <!--          <template v-if="activeLiveMenu?.param.status">-->
      <!--            <div :class="cn('flex items-center', $attrs.class ?? '')">-->
      <!--              <LinkItem-->
      <!--                v-for="menu in statusParamList"-->
      <!--                :key="menu.name"-->
      <!--                :item="{ name: menu.name, code: menu.param, href: '' }"-->
      <!--                :active="activeStatusParam.includes(menu.param)"-->
      <!--                @click="handleChangeStatus(menu)"-->
      <!--              />-->
      <!--            </div>-->
      <!--          </template>-->

      <!--          &lt;!&ndash; 排序规则 &ndash;&gt;-->
      <!--          <template v-if="activeLiveMenu?.param.sortRule">-->
      <!--            <Separator orientation="vertical" class="mx-2 hidden h-4 md:flex" />-->
      <!--            <div class="hidden h-7 items-center gap-1.5 rounded-md border p-[2px] shadow-none lg:flex">-->
      <!--              <ToggleGroup type="single" :default-value="1" :model-value="queryParams.sortRule">-->
      <!--                <ToggleGroupItem-->
      <!--                  :value="1"-->
      <!--                  class="h-[22px] w-[60px] rounded-sm p-0"-->
      <!--                  @click="handleChangeSort(1)"-->
      <!--                >-->
      <!--                  newest-->
      <!--                </ToggleGroupItem>-->
      <!--                <ToggleGroupItem-->
      <!--                  :value="2"-->
      <!--                  class="h-[22px] w-[60px] rounded-sm p-0"-->
      <!--                  @click="handleChangeSort(2)"-->
      <!--                >-->
      <!--                  hottest-->
      <!--                </ToggleGroupItem>-->
      <!--              </ToggleGroup>-->
      <!--            </div>-->
      <!--          </template>-->
      <!--          <Separator orientation="vertical" class="mx-2 hidden h-4 md:flex" />-->
      <!--          <ScheduleDialog v-if="activeLiveMenu && activeLiveMenu?.param.search"/>-->

      <!--        </div>-->
      <!--        <Separator />-->
      <!--        <ScrollArea class="flex-1 flex flex-col p-4">-->
      <!--            <EmptyPlaceholder v-if="lives.length === 0"/>-->
      <!--            <div class="grid grid-cols-5 gap-4 mb-4">-->
      <!--              <LiveCard-->
      <!--                v-for="live in lives"-->
      <!--                :key="live.id"-->
      <!--                :item="live"-->
      <!--                aspect-ratio="square"-->
      <!--                @item-click="push(`/live/detail?id=${live.id}`)"-->
      <!--              />-->
      <!--            </div>-->
      <!--        </ScrollArea>-->
      <!--        <div class="px-4 border-t bg-background h-[52px] flex items-center">-->
      <!--          <SmartPagination-->
      <!--            v-if="activeLiveMenu?.param.page && lives.length > 0"-->
      <!--            :total="total"-->
      <!--            :current-page="queryParams.pageNo"-->
      <!--            :page-size="queryParams.pageSize"-->
      <!--            @current-change="handleCurrentChange"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </div>-->
    </template>
  </ContainerWrapper>
</template>
