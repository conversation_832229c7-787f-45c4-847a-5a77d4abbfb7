<template>
  <Dialog v-model:open="isDialogOpen" :modal="showModal">
    <DialogContent class="max-w-none w-[800px]" disableOutsidePointerEvents>
      <DialogHeader>
        <DialogTitle>User Selection</DialogTitle>
      </DialogHeader>
      <Breadcrumb
        :breadcrumbs="breadcrumbs"
        class="py-0"
        @item-click="handleBreadcrumbClick"
      />
      <div class="flex flex-col border rounded-lg">
        <div class="flex items-center space-x-2 px-4 py-2">
          <Checkbox v-model="selectAll" :disabled="!hasUsers" @update:model-value="toggleSelectAll" />
          <label for="terms" class="text-sm"> Select All Users </label>
        </div>
        <Separator/>
        <div class="flex">
          <ScrollArea :style="{ height }" class="w-1/2 border-r p-2" v-loading="loading">
            <div>
              <div
                v-for="item in deptAndUserList"
                :key="item.id"
                class="flex items-center py-1 px-2 hover:bg-muted rounded-lg"
                :class="{'cursor-pointer hover:text-primary': item.type === 1}"
                @click="loadDepartment(item)"
              >
                <div :class="{ 'cursor-not-allowed': handleDisabled(item) }">
                  <Checkbox
                    v-model="item.isChecked"
                    :disabled="handleDisabled(item)"
                    class="mt-2 pointer-events-none"
                    :class="{'bg-muted': handleDisabled(item), 'border-gray-300': handleDisabled(item)}"
                  />
                </div>
                <div class="flex items-center w-full overflow-hidden ms-2">
                  <Users v-if="item.type === 1" class="size-4" />
                  <Avatar v-if="item.type === 2" class="size-4">
                    <AvatarImage :src="item.avatar || defaultAvatar" />
                  </Avatar>
                  <TooltipProvider :delay-duration="500">
                    <Tooltip>
                      <TooltipTrigger class="flex-1 min-w-0">
                        <span class="block text-left truncate ms-2 text-sm">{{ item.name }}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{{ item.name }}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <GitFork v-if="item.type == 1"  class="size-4 rotate-180" />
              </div>
            </div>
          </ScrollArea>
          <ScrollArea :style="{ height, overflow: 'auto' }" class="w-1/2 p-2 min-h-[100px]">
            <div>
              <div v-if="selectedUsers.length > 0" class="flex flex-wrap gap-2">
                <Badge
                  v-for="user in selectedUsers"
                  :key="user.id"
                  variant="outline"
                  class="py-1"
                >
                  {{ user.name }}
                    <X class="size-4 hover:text-destructive" @click="removeUser(user)" v-show="user.id !== currentUser?.id"/>
                </Badge>
              </div>
              <p v-else class="text-sm text-gray-500">No users selected</p>
            </div>
          </ScrollArea>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeDialog">Cancel</Button>
        <Button @click="confirmSelection">Confirm</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { LiveApi, DeptAndUserMixedVO } from '@/api/live/stream'
import { useUserStore } from '@/store/modules/user'
import defaultAvatar from '@/assets/live/management/default_avatar.png'
import Breadcrumb from '@/components/Breadcrumb/src/Breadcrumb.vue'
import {GitFork, Users, X} from 'lucide-vue-next'
defineOptions({ name: 'UserPickerForm' })
const props = defineProps({
  height: { type: String, default: '500px' },
  disabledUserList: { type: Array, default: () => [] },
  showModal: { type: Boolean, default: true }
})
const userStore = useUserStore()
const currentUser = computed(() => userStore.user)
interface DepartmentAndUserData extends DeptAndUserMixedVO {
  isChecked: boolean
  deptName?: string
  deptId?: number
}
const selectAll = ref(false)
const selectedUsers = ref<DepartmentAndUserData[]>([])
const deptAndUserList = ref<DepartmentAndUserData[]>([])
const breadcrumbs = ref<BreadcrumbItem[]>([])
const initDeptId = ref(0)
const loading = ref(false)
const isDialogOpen = ref(false)
const openDialog = () => {
  isDialogOpen.value = true
}
const toggleSelectAll = () => {
  const currentSelectedIds = new Set(selectedUsers.value.map((user) => user.id))
  deptAndUserList.value.forEach((item) => {
    if (item.type === 2 && item.id !== currentUser.value.id) {
      const isUserDisabled = props.disabledUserList.some(
        (user: DepartmentAndUserData) => user.id === item.id
      )
      if (!isUserDisabled) {
        if (selectAll.value) {
          if (!currentSelectedIds.has(item.id)) {
            item.isChecked = true
            selectedUsers.value.push(item)
          }
        } else {
          if (currentSelectedIds.has(item.id)) {
            item.isChecked = false
            selectedUsers.value = selectedUsers.value.filter((user) => user.id !== item.id)
          }
        }
      }
    }
  })
  emit('updateUser', selectedUsers.value)
}
const handleDisabled = (item: DepartmentAndUserData) => {
  return (
    item.type === 1 ||
    (item.type === 2 &&
      (item.id === currentUser.value.id ||
        props.disabledUserList.some((user: DepartmentAndUserData) => user.id === item.id)))
  )
}
const loadDepartment = async (item: DepartmentAndUserData) => {
  if (item.type == 1) {
    selectAll.value = false
    await getMixedDeptUserListData(item.id)
    checkAll()
    breadcrumbs.value = [...breadcrumbs.value, { id: item.id, name: item.name }]
  } else {
    if (
      item.id === currentUser.value.id ||
      props.disabledUserList.some((user: DepartmentAndUserData) => item.id === user.id)
    ) {
      return
    }
    item.isChecked = !item.isChecked
    if (item.isChecked) {
      selectedUsers.value.push(item)
    } else {
      selectedUsers.value = selectedUsers.value.filter((user) => user.id != item.id)
    }
    // emit('updateUser', selectedUsers.value)
    checkAll()
  }
}
const checkAll = () => {
  selectAll.value =
    deptAndUserList.value.filter((item) => item.type === 2 && item.isChecked).length ===
    deptAndUserList.value.filter((item) => item.type === 2).length
}
const handleBreadcrumbClick = async (id: number) => {
  await getMixedDeptUserListData(id)
  breadcrumbs.value = breadcrumbs.value.splice(
    0,
    breadcrumbs.value.findIndex((item) => item.id === id) + 1
  )
  checkAll()
}
const hasUsers = computed(() => deptAndUserList.value.some((item) => item.type === 2))
const getMixedDeptUserListData = async (deptId: number) => {
  try {
    loading.value = true
    const data = await LiveApi.getMixedDeptUserList(deptId)
    deptAndUserList.value = data.map((item: DeptAndUserMixedVO) => {
      return {
        isChecked:
          item.type === 2 &&
          (selectedUsers.value.some((user) => user.id === item.id) ||
            props.disabledUserList.some((user) => user.id === item.id)),
        ...item
      }
    })
  } catch {
    loading.value = false
  } finally {
    loading.value = false
  }
}

const removeUser = (user: DepartmentAndUserData) => {
  user.isChecked = false
  selectedUsers.value = selectedUsers.value.filter((u) => u.id !== user.id)
  //   emit('updateUser', selectedUsers.value)
}

const closeDialog = () => {
  emit('close')
  isDialogOpen.value = false
}
watch(
  () => isDialogOpen.value,
  async (newValue) => {
    if (newValue) {
      breadcrumbs.value = [{ id: initDeptId.value, name: 'Top Department' }]
      await getMixedDeptUserListData(initDeptId.value)
    }
  }
)
const confirmSelection = () => {
  emit('confirm', selectedUsers.value)
  isDialogOpen.value = false
}
defineExpose({
  selectedUsers,
  getMixedDeptUserListData,
  breadcrumbs,
  loadDepartment,
  openDialog,
  checkAll
})
const emit = defineEmits(['updateUser', 'close', 'confirm'])
// watch(selectedUsers, (newValue) => checkAll())
onMounted(async () => {
  breadcrumbs.value = [{ id: initDeptId.value, name: 'Top Department' }]
})
</script>

<style scoped>
</style>
