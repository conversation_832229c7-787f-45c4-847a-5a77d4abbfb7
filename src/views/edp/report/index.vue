<template>
  <ContainerWrapper>
    <template #content>
      <ScrollArea class="h-full w-full">
        <!-- Header 部分 -->
        <div class="mb-8 overflow-hidden">
          <div class="w-full bg-gradient-to-b from-primary to-white text-white p-10 md:p-8">
            <div class="max-w-4xl mx-auto text-center mb-20">
              <!-- Profile -->
              <div class="flex flex-col md:flex-row items-center justify-center mb-6">
                <!-- 头像骨架屏或真实头像 -->
                <template v-if="loading">
                  <Skeleton class="w-24 h-24 rounded-full border-4 border-white mb-4 md:mb-0 md:mr-6" />
                </template>
                <template v-else>
                  <Avatar class="w-24 h-24 border-4 border-white mb-4 md:mb-0 md:mr-6">
                    <AvatarImage
                      :src="userStore.getUser.avatar"
                      :alt="baseInfo?.nickname || 'User'"
                    />
                    <AvatarFallback
                      class="bg-gradient-to-br from-primary to-white text-white text-2xl"
                    >
                      <GraduationCap class="w-12 h-12" />
                    </AvatarFallback>
                  </Avatar>
                </template>

                <!-- 用户信息 -->
                <div class="text-center md:text-left">
                  <template v-if="loading">
                    <Skeleton class="h-8 w-64 mb-2 mx-auto md:mx-0" />
                    <Skeleton class="h-5 w-48 mx-auto md:mx-0" />
                  </template>
                  <template v-else>
                    <h1 class="items-center text-2xl md:text-3xl font-bold mb-2">
                      {{ baseInfo?.nickname }} | {{ baseInfo?.positionName }}
                    </h1>
                    <p class="text-blue-100 text-base"> Dept: {{ baseInfo?.deptName }} </p>
                  </template>
                </div>
              </div>

              <!-- Report Period -->
              <template v-if="loading">
                <Skeleton class="h-8 w-80 mx-auto mb-4 rounded-full" />
              </template>
              <template v-else>
                <Badge
                  variant="secondary"
                  class="bg-white/20 text-white border-white/30 px-4 py-2 text-base mb-4"
                >
                  <Calendar class="w-4 h-4 mr-2" />
                  Report Period: June 11, 2024 - June 11, 2025 (1 year)
                </Badge>
              </template>

              <!-- Summary -->
              <template v-if="loading">
                <div class="bg-white/15 rounded-lg p-4 max-w-3xl mx-auto">
                  <Skeleton class="h-6 w-40 mx-auto mb-2" />
                  <Skeleton class="h-7 w-96 mx-auto" />
                </div>
              </template>
              <template v-else>
                <div class="bg-white/15 rounded-lg p-4 max-w-3xl mx-auto">
                  <div class="flex items-center justify-center mb-2">
                    <Lightbulb class="w-5 h-5 mr-2" />
                    <span class="font-medium">Learning Profile:</span>
                  </div>
                  <p class="text-lg">
                    <strong
                      >"Active Explorer, Strong Technical Skills, Management Basics Need
                      Improvement"</strong
                    >
                  </p>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="flex flex-col gap-8 px-6">
          <!-- Core Learning Metrics Section -->
          <div>
            <div class="flex items-center mb-6">
              <div
                class="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white mr-3"
              >
                <TrendingUp class="w-5 h-5" />
              </div>
              <h2 class="text-2xl font-bold text-gray-900">Core Learning Metrics</h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <!-- Learning Engagement Card -->
              <Card class="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent class="p-6">
                  <div class="flex items-center mb-4">
                    <div
                      class="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center mr-4"
                    >
                      <Clock class="w-6 h-6 text-primary" />
                    </div>
                    <h3 class="font-semibold text-gray-900">Learning Engagement</h3>
                  </div>
                  <template v-if="loading">
                    <Skeleton class="h-9 w-32 mb-2" />
                    <Skeleton class="h-4 w-36 mb-3" />
                    <Progress :model-value="0" class="mb-3" />
                    <Skeleton class="h-4 w-full" />
                  </template>
                  <template v-else>
                    <div class="text-3xl font-bold text-primary mb-2"
                      >{{
                        coreLearningMetrics?.learningEngagement?.totalLearningDuration || '0'
                      }}
                      hrs</div
                    >
                    <p class="text-sm text-gray-600 mb-3">Total learning hours</p>
                    <Progress
                      :model-value="
                        Math.min(
                          ((coreLearningMetrics?.learningEngagement?.activeDays || 0) / 365) * 100,
                          100
                        )
                      "
                      class="mb-3"
                    />
                    <p class="text-sm text-gray-600"
                      >Active days:
                      {{ coreLearningMetrics?.learningEngagement?.activeDays || 0 }} days (Avg
                      {{ coreLearningMetrics?.learningEngagement?.avgHoursPerDay || '0' }} hrs/day)</p
                    >
                  </template>
                </CardContent>
              </Card>

              <!-- Learning Progress Card -->
              <Card class="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent class="p-6">
                  <div class="flex items-center mb-4">
                    <div
                      class="w-12 h-12 rounded-lg bg-green-50 flex items-center justify-center mr-4"
                    >
                      <CheckSquare class="w-6 h-6 text-green-600" />
                    </div>
                    <h3 class="font-semibold text-gray-900">Learning Progress</h3>
                  </div>
                  <template v-if="loading">
                    <Skeleton class="h-9 w-16 mb-2" />
                    <Skeleton class="h-4 w-32 mb-3" />
                    <Progress :model-value="0" class="mb-3" />
                    <Skeleton class="h-4 w-40" />
                  </template>
                  <template v-else>
                    <div class="text-3xl font-bold text-primary mb-2">{{
                      coreLearningMetrics?.learningProgress?.completedCount || 0
                    }}</div>
                    <p class="text-sm text-gray-600 mb-3">Completed courses</p>
                    <Progress
                      :model-value="
                        parseInt(
                          coreLearningMetrics?.learningProgress?.completionRate?.replace('%', '') ||
                            '0'
                        )
                      "
                      class="mb-3"
                    />
                    <p class="text-sm text-gray-600"
                      >Completion rate:
                      {{ coreLearningMetrics?.learningProgress?.completionRate || '0%' }}</p
                    >
                  </template>
                </CardContent>
              </Card>

              <!-- Skill Coverage Card -->
              <Card class="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent class="p-6">
                  <div class="flex items-center mb-4">
                    <div
                      class="w-12 h-12 rounded-lg bg-purple-50 flex items-center justify-center mr-4"
                    >
                      <Target class="w-6 h-6 text-purple-600" />
                    </div>
                    <h3 class="font-semibold text-gray-900">Skill Coverage</h3>
                  </div>
                  <template v-if="loading">
                    <Skeleton class="h-9 w-20 mb-2" />
                    <Skeleton class="h-4 w-36 mb-3" />
                    <Progress :model-value="0" class="mb-3" />
                    <Skeleton class="h-4 w-full" />
                  </template>
                  <template v-else>
                    <div class="text-3xl font-bold text-primary mb-2">{{
                      coreLearningMetrics?.skillCoverage?.masteryRate || '0%'
                    }}</div>
                    <p class="text-sm text-gray-600 mb-3">Job skills mastered</p>
                    <Progress
                      :model-value="
                        parseInt(
                          coreLearningMetrics?.skillCoverage?.masteryRate?.replace('%', '') || '0'
                        )
                      "
                      class="mb-3"
                    />
                    <p class="text-sm text-gray-600"
                      >{{ coreLearningMetrics?.skillCoverage?.masteredCount || 0 }} out of
                      {{ coreLearningMetrics?.skillCoverage?.total || 0 }} core skills mastered</p
                    >
                  </template>
                </CardContent>
              </Card>

              <!-- Learning Effectiveness Card -->
              <Card class="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent class="p-6">
                  <div class="flex items-center mb-4">
                    <div
                      class="w-12 h-12 rounded-lg bg-yellow-50 flex items-center justify-center mr-4"
                    >
                      <Award class="w-6 h-6 text-yellow-600" />
                    </div>
                    <h3 class="font-semibold text-gray-900">Learning Effectiveness</h3>
                  </div>
                  <template v-if="loading">
                    <Skeleton class="h-9 w-20 mb-2" />
                    <Skeleton class="h-4 w-36 mb-3" />
                    <Progress :model-value="0" class="mb-3" />
                    <Skeleton class="h-4 w-28" />
                  </template>
                  <template v-else>
                    <div class="text-3xl font-bold text-primary mb-2"
                      >{{
                        Math.round(
                          (coreLearningMetrics?.learningEffectiveness?.avgExamCorrectRate || 0) * 100
                        )
                      }}%</div
                    >
                    <p class="text-sm text-gray-600 mb-3">Average exam score</p>
                    <Progress
                      :model-value="
                        Math.round(
                          (coreLearningMetrics?.learningEffectiveness?.avgExamCorrectRate || 0) * 100
                        )
                      "
                      class="mb-3"
                    />
                    <p class="text-sm text-gray-600"
                      >Certificates:
                      {{ coreLearningMetrics?.learningEffectiveness?.gotCertificateCount || 0 }}</p
                    >
                  </template>
                </CardContent>
              </Card>
            </div>
          </div>

          <!-- Learning Progress Analysis Section -->
          <div class="">
            <div class="flex items-center mb-6">
              <div
                class="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white mr-3"
              >
                <BarChart3 class="w-5 h-5" />
              </div>
              <h2 class="text-2xl font-bold text-gray-900">Learning Progress Analysis</h2>
            </div>

            <!-- Learning Requirements Completion -->
            <Card class="mb-8">
              <CardHeader>
                <CardTitle class="flex items-center">
                  <BookOpen class="w-5 h-5 mr-2" />
                  Learning Requirements Completion
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  class="bg-gradient-to-r from-blue-50 to-cyan-50 border-l-4 border-blue-500 p-4 rounded-lg mb-4"
                >
                  <p class="font-medium text-gray-900">
                    <strong>Required Skills:</strong> Python, SQL, Data Visualization, Machine
                    Learning, Statistics, Data Cleaning, Reporting, Project Management
                  </p>
                </div>

                <div class="space-y-3">
                  <div class="flex items-start p-3 bg-green-50 rounded-lg">
                    <CheckCircle class="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                    <div>
                      <strong class="text-gray-900">Certificates Earned:</strong>
                      <span class="text-gray-700">
                        Python Data Analysis, Advanced SQL, Data Visualization Expert, Tableau
                        Certification</span
                      >
                    </div>
                  </div>

                  <div class="flex items-start p-3 bg-green-50 rounded-lg">
                    <CheckCircle class="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                    <div>
                      <strong class="text-gray-900">Courses Completed:</strong>
                      <span class="text-gray-700"> 28 out of 36 courses (78% completion)</span>
                    </div>
                  </div>

                  <div class="flex items-start p-3 bg-orange-50 rounded-lg">
                    <XCircle class="w-5 h-5 text-orange-600 mr-3 mt-0.5" />
                    <div>
                      <div class="mb-2">
                        <strong class="text-gray-900">Courses Skipped:</strong>
                        <span class="text-gray-700"> Skipped 8 learning contents of 3 skills</span>
                      </div>
                      <div class="flex items-start">
                        <Info class="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
                        <span class="text-sm text-gray-600">
                          <strong>Skip Analysis:</strong> Content perceived as too advanced, lack of
                          immediate project relevance, and overlap with existing knowledge.
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Learning Content Completion Rate -->
            <Card class="">
              <CardHeader>
                <CardTitle class="flex items-center">
                  <Brain class="w-5 h-5 mr-2" />
                  Learning content completion rate (by skill)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="flex items-center gap-3 mb-6">
                  <Label htmlFor="categoryFilter" class="font-medium text-gray-700"
                    >Filter by category:</Label
                  >
                  <Select v-model="selectedCategory">
                    <SelectTrigger class="w-64">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All categories</SelectItem>
                      <SelectItem value="hard">Hard skill</SelectItem>
                      <SelectItem value="soft">Soft skill</SelectItem>
                      <SelectItem value="hse">HSE skill</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div class="h-96">
                  <div ref="skillsChart" class="w-full h-full"></div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mt-4">
                  <div class="flex items-center justify-center text-center">
                    <Star class="w-5 h-5 text-yellow-500 mr-2" />
                    <span class="font-medium">Top Skill:</span>
                    <span class="text-blue-600 font-bold mx-2">Data Visualization (95%)</span>
                    <span class="mx-2">|</span>
                    <AlertTriangle class="w-5 h-5 text-orange-500 mr-2" />
                    <span class="font-medium">Needs Improvement:</span>
                    <span class="text-orange-600 font-bold ml-2">Project Management (60%)</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Skill Analysis Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Skill Weaknesses -->
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center">
                  <AlertTriangle class="w-5 h-5 mr-2 text-orange-500" />
                  Identify Skill Weaknesses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="p-4 bg-red-50 rounded-lg">
                    <h4 class="font-bold text-gray-900 mb-3">Machine Learning</h4>
                    <div class="flex flex-wrap gap-2">
                      <Badge variant="destructive" class="text-xs">
                        <Bug class="w-3 h-3 mr-1" />
                        Error Rate: 42%
                      </Badge>
                      <Badge variant="secondary" class="text-xs">
                        <CheckCircle class="w-3 h-3 mr-1" />
                        Completion Rate: 65%
                      </Badge>
                      <Badge variant="outline" class="text-xs">
                        <BookOpen class="w-3 h-3 mr-1" />
                        Course Access: 18
                      </Badge>
                    </div>
                  </div>

                  <div class="p-4 bg-red-50 rounded-lg">
                    <h4 class="font-bold text-gray-900 mb-3">Project Management</h4>
                    <div class="flex flex-wrap gap-2">
                      <Badge variant="destructive" class="text-xs">
                        <Bug class="w-3 h-3 mr-1" />
                        Error Rate: 38%
                      </Badge>
                      <Badge variant="secondary" class="text-xs">
                        <CheckCircle class="w-3 h-3 mr-1" />
                        Completion Rate: 58%
                      </Badge>
                      <Badge variant="outline" class="text-xs">
                        <BookOpen class="w-3 h-3 mr-1" />
                        Course Access: 12
                      </Badge>
                    </div>
                  </div>
                </div>

                <div class="mt-6">
                  <h4 class="font-medium text-gray-900 mb-3">Weakness Distribution Analysis</h4>
                  <div class="h-64">
                    <div ref="weaknessChart" class="w-full h-full"></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Learning Interests -->
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center">
                  <Heart class="w-5 h-5 mr-2 text-red-500" />
                  Discover Learning Interests
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="p-4 bg-green-50 rounded-lg">
                    <h4 class="font-bold text-gray-900 mb-3">Data Visualization</h4>
                    <div class="flex flex-wrap gap-2">
                      <Badge variant="default" class="text-xs bg-yellow-500">
                        <Star class="w-3 h-3 mr-1" />
                        Rating: 4.8
                      </Badge>
                      <Badge variant="outline" class="text-xs">
                        <BookOpen class="w-3 h-3 mr-1" />
                        TNI Requests: False
                      </Badge>
                      <Badge variant="secondary" class="text-xs bg-green-500 text-white">
                        <CheckCircle class="w-3 h-3 mr-1" />
                        Completion Rate: 95%
                      </Badge>
                    </div>
                  </div>

                  <div class="p-4 bg-green-50 rounded-lg">
                    <h4 class="font-bold text-gray-900 mb-3">AI Fundamentals</h4>
                    <div class="flex flex-wrap gap-2">
                      <Badge variant="default" class="text-xs bg-yellow-500">
                        <Star class="w-3 h-3 mr-1" />
                        Rating: 4.5
                      </Badge>
                      <Badge variant="outline" class="text-xs">
                        <BookOpen class="w-3 h-3 mr-1" />
                        TNI Requests: True
                      </Badge>
                      <Badge variant="secondary" class="text-xs bg-green-500 text-white">
                        <CheckCircle class="w-3 h-3 mr-1" />
                        Completion Rate: 85%
                      </Badge>
                    </div>
                  </div>
                </div>

                <div class="mt-6">
                  <h4 class="font-medium text-gray-900 mb-3">Top Rated Skills</h4>
                  <div class="h-64">
                    <div ref="ratingChart" class="w-full h-full"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Learning Method Analysis Section -->
          <div class="">
            <div class="flex items-center mb-6">
              <div
                class="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white mr-3"
              >
                <ClipboardList class="w-5 h-5" />
              </div>
              <h2 class="text-2xl font-bold text-gray-900">Learning Method Analysis</h2>
            </div>

            <div class="grid grid-cols-1 gap-6">
              <!-- Learning Time Analysis -->
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center">
                    <Clock class="w-5 h-5 mr-2" />
                    Learning Time Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="h-80">
                    <div ref="timeChart" class="w-full h-full"></div>
                  </div>

                  <div class="mt-4 p-4 bg-orange-50 rounded-lg">
                    <div class="flex items-start">
                      <Moon class="w-5 h-5 text-orange-600 mr-3 mt-0.5" />
                      <div>
                        <strong class="text-gray-900">Late-Night Learning:</strong>
                        <span class="text-gray-700">
                          70% of learning concentrated between 22:00-02:00, recommend adjusting
                          schedule</span
                        >
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <!-- Learning Improvement Recommendations -->
          <div class="mb-6">
            <div class="flex items-center mb-6">
              <div
                class="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white mr-3"
              >
                <Lightbulb class="w-5 h-5" />
              </div>
              <h2 class="text-2xl font-bold text-gray-900">Learning Improvement Recommendations</h2>
            </div>

            <Card class="bg-gradient-to-br from-blue-50 to-cyan-50">
              <CardContent class="p-6">
                <Card class="bg-white">
                  <CardHeader>
                    <CardTitle class="flex items-center text-blue-600">
                      <GraduationCap class="w-5 h-5 mr-2" />
                      Learning Method Optimization
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div class="space-y-3">
                      <div class="flex items-start">
                        <Check class="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                        <span class="text-gray-700"
                          >Time Management: Create daily learning schedule, avoid late-night
                          sessions</span
                        >
                      </div>
                      <div class="flex items-start">
                        <Check class="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                        <span class="text-gray-700"
                          >Deep Learning: Reduce video skip rate (current 35% → target 15%)</span
                        >
                      </div>
                      <div class="flex items-start">
                        <Check class="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                        <span class="text-gray-700"
                          >Practice Focus: Complete 2 data analysis projects weekly</span
                        >
                      </div>
                      <div class="flex items-start">
                        <Check class="w-5 h-5 text-green-600 mr-3 mt-0.5" />
                        <span class="text-gray-700"
                          >Regular Review: Weekly review of notes and mistakes</span
                        >
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </div>
        </div>

        <!-- Footer -->
      </ScrollArea>
    </template>
  </ContainerWrapper>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { ContainerWrapper } from '@/components/ContainerWrap'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Skeleton } from '@/components/ui/skeleton'
import { useUserStore } from '@/store/modules/user'
import {
  GraduationCap,
  Calendar,
  Lightbulb,
  TrendingUp,
  Clock,
  CheckSquare,
  Target,
  Award,
  BarChart3,
  BookOpen,
  CheckCircle,
  XCircle,
  Info,
  Brain,
  Star,
  AlertTriangle,
  Bug,
  Heart,
  ClipboardList,
  Moon,
  Check
} from 'lucide-vue-next'
import {
  getPersonBaseInfo,
  PersonBaseInfoVO,
  getCoreLearningMetrics,
  CoreLearningMetricsVO
} from '@/api/edp/report'
// ECharts imports
import * as echarts from 'echarts'

/** ----- SETUP ----- */
const userStore = useUserStore()
const queryParams = ref({
  year: 2025
}) // 查询参数
const baseInfo = ref<PersonBaseInfoVO>() // 用户基本信息
const coreLearningMetrics = ref<CoreLearningMetricsVO>() // 核心学习指标
const loading = ref(true) // 加载状态
// Reactive data
const selectedCategory = ref('hard')
const skillsChart = ref<HTMLDivElement>()
const weaknessChart = ref<HTMLDivElement>()
const ratingChart = ref<HTMLDivElement>()
const timeChart = ref<HTMLDivElement>()
let myChart: echarts.ECharts | null = null

/** ----- FUNCTIONS ----- */
/** 获取用户基本信息 */
const getBaseInfo = async () => {
  try {
    baseInfo.value = await getPersonBaseInfo(queryParams.value)
  } catch (error) {
    console.log('获取用户基本信息失败:', error)
  }
}

/** 获取核心学习指标 */
const getCoreMetrics = async () => {
  try {
    coreLearningMetrics.value = await getCoreLearningMetrics(queryParams.value)
  } catch (error) {
    console.log('获取核心学习指标失败:', error)
  }
}

/** */

// Generate skill names for different categories
function generateSkillNamesForCategory(category: string): string[] {
  const skillCategories = [
    'Data Analysis',
    'Machine Learning',
    'Data Engineering',
    'Data Visualization',
    'Cloud Computing',
    'Database',
    'Programming',
    'Statistics',
    'Business Intelligence',
    'DevOps',
    'Big Data',
    'AI'
  ]

  const skillLevels = ['Basic', 'Intermediate', 'Advanced', 'Expert']
  const skillTypes = ['Techniques', 'Methods', 'Systems', 'Frameworks', 'Tools']

  let count: number
  switch (category) {
    case 'hard':
      count = 15
      break
    case 'soft':
      count = 20
      break
    case 'hse':
      count = 10
      break
    default:
      count = 60
  }

  const skills: string[] = []
  for (let i = 0; i < count; i++) {
    const categoryIndex = Math.floor(Math.random() * skillCategories.length)
    const levelIndex = Math.floor(Math.random() * skillLevels.length)
    const typeIndex = Math.floor(Math.random() * skillTypes.length)

    skills.push(
      `${skillCategories[categoryIndex]} ${skillLevels[levelIndex]} ${skillTypes[typeIndex]}`
    )
  }
  return skills
}

// Generate random data for the radar chart based on category
function generateRadarDataForCategory(count: number): number[] {
  const data: number[] = []
  for (let i = 0; i < count; i++) {
    // Generate values between 20 and 95
    data.push(Math.floor(Math.random() * 76) + 20)
  }
  return data
}

// Initialize charts
function initializeCharts() {
  if (!skillsChart.value || !weaknessChart.value || !ratingChart.value || !timeChart.value) return

  // Radar Chart (skillsChart)
  if (skillsChart.value) {
    const initialCategory = 'hard'
    const skillNames = generateSkillNamesForCategory(initialCategory)
    const skillData = generateRadarDataForCategory(15)

    myChart = echarts.init(skillsChart.value)
    const radarOption = {
      title: {
        text: 'Learning Content Completion Rate',
        left: 'center'
      },
      tooltip: {},
      legend: {
        data: ['Completion Rate %'],
        bottom: 0
      },
      radar: {
        indicator: skillNames.map((name) => ({ name, max: 100 })),
        radius: '60%'
      },
      series: [
        {
          name: 'Learning Content Completion Rate',
          type: 'radar',
          data: [
            {
              value: skillData,
              name: 'Completion Rate %',
              areaStyle: {
                color: 'rgba(58, 157, 255, 0.2)'
              },
              lineStyle: {
                color: 'rgb(58, 157, 255)'
              },
              itemStyle: {
                color: 'rgb(58, 157, 255)'
              }
            }
          ]
        }
      ]
    }
    myChart.setOption(radarOption)
  }

  // Weakness Chart (bar)
  if (weaknessChart.value) {
    const weaknessSkills = [
      'Deep Learning',
      'Project Management',
      'Data Mining',
      'Big Data',
      'Cloud Computing'
    ]
    const weaknessErrorRates = [56, 48, 45, 42, 38]

    const weaknessChartInstance = echarts.init(weaknessChart.value)
    const weaknessOption = {
      title: {
        text: 'Weakness Distribution Analysis',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: weaknessSkills,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: 'Error Rate (%)',
        max: 60
      },
      series: [
        {
          data: weaknessErrorRates,
          type: 'bar',
          itemStyle: {
            color: '#ff6b6b'
          }
        }
      ]
    }
    weaknessChartInstance.setOption(weaknessOption)
  }

  // Rating Chart (bar)
  if (ratingChart.value) {
    const ratingSkills = [
      'Data Visualization',
      'AI Fundamentals',
      'Python Programming',
      'Data Engineering',
      'Machine Learning'
    ]
    const ratingValues = [4.8, 4.7, 4.6, 4.5, 4.4]

    const ratingChartInstance = echarts.init(ratingChart.value)
    const ratingOption = {
      title: {
        text: 'Top Rated Skills',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: ratingSkills,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: 'Rating (out of 5)',
        max: 5
      },
      series: [
        {
          data: ratingValues,
          type: 'bar',
          itemStyle: {
            color: '#4caf50'
          }
        }
      ]
    }
    ratingChartInstance.setOption(ratingOption)
  }

  // Time Chart (pie)
  if (timeChart.value) {
    const timeChartInstance = echarts.init(timeChart.value)
    const timeOption = {
      title: {
        text: 'Learning Time Distribution by Period',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: 'Learning Time',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 5, name: '00-06', itemStyle: { color: '#3a9dff' } },
            { value: 15, name: '07-12', itemStyle: { color: '#4caf50' } },
            { value: 10, name: '13-18', itemStyle: { color: '#ffd166' } },
            { value: 70, name: '19-23', itemStyle: { color: '#ff6b6b' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    timeChartInstance.setOption(timeOption)
  }
}

// Watch for category changes
watch(selectedCategory, (newCategory) => {
  if (!myChart || !skillsChart.value) return

  let count: number
  switch (newCategory) {
    case 'hard':
      count = 15
      break
    case 'soft':
      count = 20
      break
    case 'hse':
      count = 10
      break
    default:
      count = 60
  }

  const newSkillNames = generateSkillNamesForCategory(newCategory)
  const newSkillData = generateRadarDataForCategory(count)

  myChart.dispose()

  if (skillsChart.value) {
    myChart = echarts.init(skillsChart.value)
    const radarOption = {
      title: {
        text: 'Learning Content Completion Rate',
        left: 'center'
      },
      tooltip: {},
      legend: {
        data: ['Completion Rate %'],
        bottom: 0
      },
      radar: {
        indicator: newSkillNames.map((name) => ({ name, max: 100 })),
        radius: '60%'
      },
      series: [
        {
          name: 'Learning Content Completion Rate',
          type: 'radar',
          data: [
            {
              value: newSkillData,
              name: 'Completion Rate %',
              areaStyle: {
                color: 'rgba(58, 157, 255, 0.2)'
              },
              lineStyle: {
                color: 'rgb(58, 157, 255)'
              },
              itemStyle: {
                color: 'rgb(58, 157, 255)'
              }
            }
          ]
        }
      ]
    }
    myChart.setOption(radarOption)
  }
})

/** ----- LIFECYCLE HOOKS ----- */
onMounted(async () => {
  try {
    await Promise.all([
      getBaseInfo(), // 获取用户基本信息
      getCoreMetrics() // 获取核心学习指标
    ])
  } finally {
    loading.value = false // 无论成功失败都关闭加载状态
  }
  
  nextTick(() => {
    initializeCharts()
  })
})
</script>
