<script lang="ts" setup>
import { ref, onMounted, computed, reactive } from 'vue'
import {
  DateFormatter,
  getLocalTimeZone,
  parseDate,
  today,
  type DateValue
} from '@internationalized/date'
import {
  CalendarIcon,
  ChevronsUpDown,
  ChevronsDownUp,
  RefreshCcw,
  Clock,
  ArrowRight,
  LibraryBig,
  BookUser,
  Search
} from 'lucide-vue-next'
import { toDate } from 'reka-ui/date'
import {
  StudyPlanApi,
  StudyPlanReqVO,
  StudyPlanRespVO,
  PhaseEnum,
  StudyContentTypeEnum
} from '@/api/edp/studyplan'
import { Pagination, PaginationNext, PaginationPrev } from '@/components/ui/pagination'
import SkipForm from '@/views/edp/studyplan/SkipForm.vue'
import BeginnerCover from '@/assets/edp/beginner.jpg'
import IntermediateCover from '@/assets/edp/intermediate.jpg'
import AdvancedCover from '@/assets/edp/advanced.jpg'
import { useRoute, useRouter } from 'vue-router'
import { Calendar } from '@/components/ui/calendar'

/** ----- INTERFACE ----- */
interface TabItem {
  value: number
  label: string
}

interface ChartItem {
  name: string
  total: number
}

/** ----- SETUP ----- */
const route = useRoute()
const router = useRouter()
const phaseStatistic = ref() // 定义阶段统计数据
const total = ref(0)
const selectedSkillId = ref(0)
const loading = ref(false) // 加载状态
const tabList = ref<TabItem[]>([
  {
    value: PhaseEnum.PHASE1,
    label: 'Phase 1'
  },
  {
    value: PhaseEnum.PHASE2,
    label: 'Phase 2'
  },
  {
    value: PhaseEnum.PHASE3,
    label: 'Phase 3'
  }
]) // 定义Tab列表
const contentTypeList = ref([
  {
    value: StudyContentTypeEnum.COURSE,
    label: 'Course'
  },
  {
    value: StudyContentTypeEnum.TRAINING,
    label: 'Training'
  },
  {
    value: StudyContentTypeEnum.KNOWLEDGE,
    label: 'Knowledge'
  }
]) // 定义学习内容类型列表
const studyPlanList = ref<StudyPlanRespVO[]>([]) // 定义学习计划列表

// 
const queryParams = reactive<StudyPlanReqVO>({
  phase: Number(route.query.phase) || PhaseEnum.PHASE1,
  positionId: Number(route.query.positionId),
  bizType: undefined,
  title: '',
  pageNo: 1,
  pageSize: 10
}) // 学习计划列表查询参数对象请求体

// 当前phase值的ref，用于确保响应式
const currentPhase = computed(() => queryParams.phase)
const charts = ref<ChartItem[]>([]) // progress数据

// Dialog 相关状态
const scheduleDialogOpen = ref(false)
const currentPlan = ref<StudyPlanRespVO | null>(null)
const scheduleForm = reactive({
  startDate: undefined as DateValue | undefined,
  endDate: undefined as DateValue | undefined
})

// 日期格式化器
const df = new DateFormatter('en-US', {
  dateStyle: 'medium'
})

// 计算属性用于确保类型正确
const startDateValue = computed(() => scheduleForm.startDate as DateValue | undefined)
const endDateValue = computed(() => scheduleForm.endDate as DateValue | undefined)
const minStartDate = computed(() => today(getLocalTimeZone()) as DateValue)
const minEndDate = computed(() => (scheduleForm.startDate || today(getLocalTimeZone())) as DateValue)

// 根据当前阶段返回对应的封面图片
const currentPhaseCover = computed(() => {
  switch (currentPhase.value) {
    case PhaseEnum.PHASE1:
      return BeginnerCover
    case PhaseEnum.PHASE2:
      return IntermediateCover
    case PhaseEnum.PHASE3:
      return AdvancedCover
    default:
      return BeginnerCover
  }
})

/** ----- FUNCTIONS ------ */
/** 获取阶段统计 */
const getPhaseStatistic = async () => {
  try {
    loading.value = true // 开始加载

    // 定义请求体
    const params = {
      phase: queryParams.phase,
      positionId: Number(queryParams.positionId)
    }
    console.log('获取阶段统计请求参数:', params)

    // 调用API并存储阶段统计数据
    const res = await StudyPlanApi.getPhaseStatistics(params)
    phaseStatistic.value = res
    console.log('获取阶段统计成功😄:', phaseStatistic.value)
    // 更新charts数据
    charts.value = res.charts
  } catch (error) {
    console.log('获取阶段统计失败😭:', error)
  } finally {
    loading.value = false // 结束加载
  }
}

/** 获取学习计划分页 */
const getStudyPlan = async () => {
  try {
    loading.value = true // 开始加载
    // API调用并存储学习计划列表
    const res = await StudyPlanApi.getStudyPlanPage(queryParams)
    studyPlanList.value = res.list
    total.value = res.total

    console.log('获取学习计划分页成功:', studyPlanList.value)
  } catch (error) {
    console.log('获取学习计划分页失败:', error)
  } finally {
    loading.value = false // 结束加载
  }
}

/** 当前阶段级别文本（响应式） */
const currentPhaseLevel = computed(() => {
  console.log('Phase changed:', currentPhase.value)
  return getPhaseLevelText(currentPhase.value)
})

/** 获取阶段级别文本 */
function getPhaseLevelText(phase: number): string {
  switch (phase) {
    case PhaseEnum.PHASE1:
      return 'Beginner'
    case PhaseEnum.PHASE2:
      return 'Intermediate'
    case PhaseEnum.PHASE3:
      return 'Advanced'
    default:
      return 'Unknown'
  }
}

/** 计算日期间隔天数 */
function getDaysDiff(startDate: string, endDate: string): number {
  // 将日期字符串转换为Date对象
  const start = new Date(startDate)
  const end = new Date(endDate)

  // 计算时间差（毫秒）
  const timeDiff = end.getTime() - start.getTime()

  // 将毫秒转换为天数
  return Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
}

/** 计算进度百分比 */
function getProgressPercent(progress: string | number): number {
  if (typeof progress === 'number') {
    return Math.round(progress)
  }

  // 处理分数格式 (例如: "1/3")
  if (progress && progress.includes('/')) {
    const [numerator, denominator] = progress.split('/').map(Number)
    if (!isNaN(numerator) && !isNaN(denominator) && denominator !== 0) {
      return Math.round((numerator / denominator) * 100)
    }
  }

  // 如果无法解析，返回0
  return 0
}

/** 打开设置计划 Dialog */
const handleSetSchedule = (plan: StudyPlanRespVO) => {
  currentPlan.value = plan

  // 设置默认日期
  const defaultStartDate = plan.startDate ? parseDate(plan.startDate) : today(getLocalTimeZone())
  const defaultEndDate = plan.endDate
    ? parseDate(plan.endDate)
    : today(getLocalTimeZone()).add({ months: 1 })

  scheduleForm.startDate = defaultStartDate as DateValue
  scheduleForm.endDate = defaultEndDate as DateValue
  scheduleDialogOpen.value = true
}

/** 确认设置计划 */
const confirmSchedule = async () => {
  if (!currentPlan.value) return

  try {
    loading.value = true

    const data = {
      id: currentPlan.value.id,
      positionId: Number(queryParams.positionId),
      phase: queryParams.phase,
      skillId: currentPlan.value.skillId,
      startDate: scheduleForm.startDate?.toString(),
      endDate: scheduleForm.endDate?.toString(),
      studyPlanContentList: currentPlan.value.studyPlanContentList.map((content) => ({
        planContentId: content.planContentId,
        recommendId: content.recommendId,
        bizType: content.bizType,
        title: content.title,
        skillId: content.skillId || currentPlan.value!.skillId,
        planId: content.planId,
        status: content.status,
        skipped: content.skipped || false,
        skipReason: content.skipReason || ''
      }))
    }

    if (currentPlan.value.id) {
      await StudyPlanApi.updateStudyPlan(data)
    } else {
      await StudyPlanApi.createStudyPlan(data)
    }

    scheduleDialogOpen.value = false
    await getStudyPlan() // 刷新列表
  } catch (error) {
    console.error('设置学习计划失败:', error)
  } finally {
    loading.value = false
  }
}

/** 计算学习进度状态 */
function getProgressStatus(status: number) {
  switch (status) {
    case 1:
      return 'Not Started'
    case 2:
      return 'Studying'
    case 3:
      return 'Completed'
  }
}

/** 处理Tab点击 */
const handleTabClick = (value: number) => {
  console.log('Tab clicked:', value)
  queryParams.phase = value
  selectedSkillId.value = 0
  getPhaseStatistic()
  getStudyPlan()
}

/** 格式化进度值为百分比 */
function valueFormatter(value: number) {
  return `${value}%`.toString()
}

/** 计算完成百分比 */
const completedPercentage = computed(() => {
  if (!charts.value.length) return 0

  const totalSum = charts.value.reduce((sum, item) => sum + item.total, 0)
  if (totalSum === 0) return 0

  // 查找 "Completed" 状态的数据
  const completedItem = charts.value.find(item =>
    item.name.toLowerCase().includes('completed') ||
    item.name.toLowerCase().includes('完成')
  )

  if (!completedItem) return 0

  return Math.round((completedItem.total / totalSum) * 100)
})

/** 自定义值格式化器 - 用于中心标签和tooltip */
function customValueFormatter(value: number) {
  // 如果是总值（用于中心标签），显示完成百分比
  const totalSum = charts.value.reduce((sum, item) => sum + item.total, 0)
  if (value === totalSum) {
    return `${completedPercentage.value}%`
  }
  // 否则显示原始值（用于tooltip）
  return `${value}%`
}

/** 跳转到内容详情页 */
const toContentDetail = (planContentId: string | number) => {
  router.push({
    path: `/content/detail/${planContentId}`
  })
}

/** 移除学习计划 */
const removeStudyPlan = async (id: number) => {
  try {
    loading.value = true
    await StudyPlanApi.removeStudyPlan(id)
    await getStudyPlan() // 刷新列表
  } catch (error) {
    console.error('移除学习计划失败:', error)
  } finally {
    loading.value = false
  }
}

/** LIFECYCLE HOOK */
onMounted(() => {
  getPhaseStatistic()
  getStudyPlan()
})
</script>

<template>
  <ContainerWrap
    title="My Development Plan"
    description="Personalized learning path to accelerate your career development"
  >
    <div class="container py-5 flex flex-col gap-5">
      <!--Tab页-->
      <Tabs
        :default-value="queryParams.phase"
        class="w-[400px]"
        @update:model-value="handleTabClick"
      >
        <TabsList class="grid grid-cols-3 w-full">
          <TabsTrigger v-for="tab in tabList" :key="tab.value" :value="tab.value">
            {{ tab.label }}
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <!--内容-->
      <div class="flex flex-col gap-10">
        <!--封面信息容器-->
        <div class="flex items-center justify-between gap-10">
          <!--封面信息-->
          <div class="flex gap-5 p-0">
            <!--封面图片-->
            <img
              :src="currentPhaseCover"
              :alt="`${currentPhaseLevel} Phase Cover`"
              class="rounded-md object-cover aspect-w-16 aspect-h-9 h-[200px]"
            />

            <!--阶段信息-->
            <div class="flex flex-col items-start justify-between">
              <!--阶段描述-->
              <div class="flex flex-col items-start gap-5">
                <h1 class="text-lg">
                  The key to unlocking a new chapter in your career development is ready for you!
                  The learning content plan tailored for you by your coach and manager reflects
                  their recognition of your potential!
                </h1>

                <Badge variant="secondary" class="rounded-md pointer-events-none">
                  {{ currentPhaseLevel }}
                </Badge>
              </div>

              <!--学员、岗位信息-->
              <div class="flex gap-10">
                <!--学习人数-->
                <div class="flex gap-1 items-center text-muted-foreground">
                  <LibraryBig :size="20" />
                  <p> Number of studies: {{ phaseStatistic?.studies || 0 }} </p>
                </div>

                <!--岗位信息-->
                <div
                  v-if="phaseStatistic?.position"
                  class="flex gap-1 items-center text-muted-foreground"
                >
                  <BookUser :size="20" />
                  <p> Position: {{ phaseStatistic?.position }} </p>
                </div>
              </div>
            </div>
          </div>

          <!--完成进度-->
          <div class="flex items-center gap-2">
            <DonutChart
              index="name"
              :category="'total'"
              :data="charts"
              :value-formatter="customValueFormatter"
              :colors="['#017B3D', '#D9D9D9', '#77D5A5', '#D9D9D9']"
            />

            <!-- 图表数据图例 -->
            <div class="flex flex-col gap-3 w-full">
              <div
                v-for="(item, index) in charts"
                :key="index"
                class="flex items-center gap-2 w-full whitespace-nowrap overflow-hidden text-ellipsis"
              >
                <div
                  class="w-3 h-3 rounded-full shrink-0"
                  :style="{
                    backgroundColor: ['#017B3D', '#D9D9D9', '#77D5A5', '#D9D9D9'][index % 4]
                  }"
                ></div>
                <span class="text-sm truncate">{{ item.name }}: {{ item.total }}</span>
              </div>
            </div>
          </div>
        </div>

        <!--学习内容-->
        <div class="flex flex-col gap-10">
          <!--Actions-->
          <div class="flex gap-4 w-full">
            <!--搜索框-->
            <SuperSearch v-model="queryParams.title" @keyup="getStudyPlan" @search="getStudyPlan" />

            <!--内容分类筛选-->
            <Select v-model="queryParams.bizType" @update:model-value="getStudyPlan">
              <SelectTrigger class="w-auto">
                <SelectValue placeholder="Content Type" class="mr-3" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel> Content Type </SelectLabel>
                  <SelectItem v-for="type in contentTypeList" :key="type.value" :value="type.value">
                    {{ type.label }}
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <!--内容列表折叠版-->
          <div v-loading="loading" class="flex flex-col gap-5">
            <template v-if="studyPlanList.length > 0">
              <div v-for="plan in studyPlanList" :key="plan.skillId" class="flex flex-col gap-5">
                <Collapsible :open="selectedSkillId === plan.skillId" class="space-y-3">
                  <!--内容列表-技能标题-->
                  <div class="flex justify-between">
                    <!--技能标题容器-->
                    <div class="flex flex-col gap-2">
                      <!--技能标题-->
                      <div class="flex gap-2">
                        <div class="flex gap-2">
                          <!--折叠控制器-->
                          <CollapsibleTrigger as-child>
                            <Button
                              variant="ghost"
                              size="icon"
                              class="w-9 p-0"
                              @click="
                                selectedSkillId =
                                  selectedSkillId === plan.skillId ? 0 : plan.skillId
                              "
                            >
                              <ChevronsDownUp
                                v-if="selectedSkillId === plan.skillId"
                                class="h-4 w-4 transition-transform duration-300 ease-in-out"
                              />
                              <ChevronsUpDown
                                v-else
                                class="h-4 w-4 transition-transform duration-300 ease-in-out"
                              />
                            </Button>
                          </CollapsibleTrigger>

                          <div class="flex gap-2 h-full items-center justify-center">
                            <H1
                              :class="[
                                'text-xl items-center justify-center',
                                plan.status === 3
                                  ? 'line-through decoration-2 decoration-gray-500 opacity-60'
                                  : ''
                              ]"
                            >
                              {{ plan.skillName }}
                            </H1>
                          </div>
                        </div>

                        <!--技能完成进度-->
                        <div class="flex gap-2 items-center justify-center">
                          <Badge variant="secondary" class="rounded-md pointer-events-none">
                            {{ plan.firstLevelSkillName }}
                          </Badge>

                          <Badge
                            class="rounded-md pointer-events-none"
                            :class="{
                              'bg-primary/10 text-primary': plan.status === 1,
                              'bg-muted-foreground text-muted-foreground': plan.status === 3
                            }"
                          >
                            {{ getProgressStatus(plan.status) }}
                          </Badge>
                          <p> {{ plan.progress }} </p>
                        </div>
                      </div>

                      <!--时间范围-->
                      <div
                        v-if="plan.startDate && plan.endDate"
                        class="flex gap-1 items-center pl-11 text-muted-foreground"
                      >
                        <Clock :size="16" />
                        <p class="text-sm">
                          {{ plan.startDate + ` ~ ` + plan.endDate }}
                          {{ `(${getDaysDiff(plan.startDate, plan.endDate)} days)` }}
                        </p>
                      </div>
                    </div>

                    <!--操作按钮-->
                    <div class="flex gap-2">
                      <!--跳过此技能全部课程内容表单弹窗 -->
                      <SkipForm
                        v-if="
                          plan.studyPlanContentList.some((c) => c.status === 1) &&
                          !plan.studyPlanContentList.some((c) => c.skipped) &&
                          plan.status !== 3
                        "
                        :id="plan.id"
                        :phase="currentPhase"
                        :contentIds="
                          plan.studyPlanContentList.map((c) => c.planContentId).join(',')
                        "
                        :label="'Skip All'"
                        @refresh-list="getStudyPlan"
                      />

                      <Button
                        v-if="plan.status === 3"
                        variant="outline"
                        @click="removeStudyPlan(plan.id)"
                      >
                        Remove
                      </Button>

                      <Button
                        v-else
                        :disabled="
                          plan.studyPlanContentList.every((c) => c.status === 1) && plan.id
                        "
                        variant="outline"
                        @click="handleSetSchedule(plan)"
                      >
                        Set Schedule
                      </Button>
                    </div>
                  </div>

                  <!--内容列表-学习内容-->
                  <CollapsibleContent class="space-y-2">
                    <div
                      v-for="content in plan.studyPlanContentList"
                      :key="content.planContentId"
                      :class="[
                        'grid grid-cols-12 gap-20 items-center px-8 py-3 bg-[#F1F5F999] rounded-lg w-full transition-all duration-200',
                        plan.status === 3
                          ? 'opacity-60 line-through decoration-2 decoration-gray-500'
                          : 'hover:bg-primary/5'
                      ]"
                    >
                      <!--课程名称 - 6个栅格-->
                      <div class="col-span-6 flex gap-2 items-center min-w-0">
                        <p class="truncate" :title="content.title"> {{ content.title }} </p>
                        <Badge
                          v-if="content.bizType"
                          variant="secondary"
                          class="bg-[#E5E5E5] rounded-md pointer-events-none flex-shrink-0"
                        >
                          {{
                            contentTypeList.find((type) => type.value === content.bizType)?.label
                          }}
                        </Badge>
                      </div>

                      <!--课程学习进度 - 3个栅格-->
                      <div class="col-span-4 flex items-center gap-2">
                        <Progress
                          :model-value="getProgressPercent(content.progress)"
                          class="flex-1"
                        />
                        <p class="text-sm whitespace-nowrap">
                          {{ getProgressPercent(content.progress) + `%` }}
                        </p>
                      </div>

                      <!--课程操作按钮 - 3个栅格-->
                      <div class="col-span-2 flex gap-2 justify-end">
                        <!--跳过单个课程内容表单弹窗-->
                        <SkipForm
                          v-if="content.status === 1 && !content.skipped && plan.status !== 3"
                          :id="plan.id"
                          :contentIds="(content.planContentId || '').toString()"
                          :label="'Skip'"
                          @refresh-list="getStudyPlan"
                        />

                        <!--学习按钮&课程详情跳转按钮-->
                        <Button
                          :disabled="
                            (plan.status === 1 && content.status != 1) ||
                            content.skipped ||
                            plan.status === 3
                          "
                          :class="[
                            'group',
                            plan.status === 3 ? 'opacity-50 cursor-not-allowed' : ''
                          ]"
                          @click="plan.status !== 3 ? toContentDetail(content.planContentId) : null"
                        >
                          <span> Study </span>
                          <ArrowRight
                            :class="[
                              'transition-transform duration-300',
                              plan.status !== 3 ? 'group-hover:translate-x-1' : ''
                            ]"
                          />
                        </Button>
                      </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            </template>

            <!-- 空状态 -->
            <EmptyPlaceholder v-else />
          </div>
        </div>

        <!--分页-->
        <div v-if="studyPlanList.length > 0" class="flex justify-center mb-10">
          <Pagination
            :items-per-page="queryParams.pageSize"
            :total="total"
            :page="queryParams.pageNo"
            @update:page="
              (page) => {
                queryParams.pageNo = page
                getStudyPlan()
              }
            "
          >
            <div class="flex items-center gap-1">
              <PaginationPrev />

              <template
                v-for="(item, index) in Array.from(
                  { length: Math.ceil(total / queryParams.pageSize) },
                  (_, i) => i + 1
                )"
                :key="index"
              >
                <Button
                  :variant="item === queryParams.pageNo ? 'default' : 'outline'"
                  @click="
                    () => {
                      queryParams.pageNo = item
                      getStudyPlan()
                    }
                  "
                >
                  {{ item }}
                </Button>
              </template>

              <PaginationNext />
            </div>
          </Pagination>
        </div>
      </div>
    </div>
  </ContainerWrap>

  <!-- Schedule Dialog -->
  <Dialog v-model:open="scheduleDialogOpen">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Set Study Schedule</DialogTitle>
        <DialogDescription> Choose start and end dates for your study plan. </DialogDescription>
      </DialogHeader>

      <div class="grid gap-6 py-4">
        <!-- Start Date -->
        <div class="grid gap-2">
          <Label>Start Date</Label>
          <Popover>
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                :class="[
                  'w-full text-left font-normal items-center justify-between',
                  !scheduleForm.startDate && 'text-muted-foreground'
                ]"
              >
                {{
                  scheduleForm.startDate
                    ? df.format(toDate(scheduleForm.startDate))
                    : 'Pick start date'
                }}
                <CalendarIcon class="mr-2 h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="start">
              <Calendar
                :model-value="startDateValue"
                :min-value="minStartDate"
                initial-focus
                @update:model-value="(date) => scheduleForm.startDate = date"
              />
            </PopoverContent>
          </Popover>
        </div>

        <!-- End Date -->
        <div class="grid gap-2">
          <Label>End Date</Label>
          <Popover>
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                :class="[
                  'w-full text-left font-normal items-center justify-between',
                  !scheduleForm.endDate && 'text-muted-foreground'
                ]"
              >
                {{
                  scheduleForm.endDate ? df.format(toDate(scheduleForm.endDate)) : 'Pick end date'
                }}
                <CalendarIcon class="mr-2 h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="start">
              <Calendar
                :model-value="endDateValue"
                :min-value="minEndDate"
                initial-focus
                @update:model-value="(date) => scheduleForm.endDate = date"
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="scheduleDialogOpen = false"> Cancel </Button>
        <Button @click="confirmSchedule" :disabled="loading"> Confirm </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
