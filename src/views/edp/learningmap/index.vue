<script lang="ts" setup>
import {
  LearningMapApi,
  PhaseEnum,
  PhaseLearningMap,
  FirstLevelSkillEnum
} from '@/api/edp/learningmap'
import type { PostVO } from '@/api/system/post'
import { getPositionListForCurrentUser } from '@/api/system/user'
import { CircularProgress } from '@/components/Progress'
import { useRouter, useRoute } from 'vue-router'
import { ref, onMounted } from 'vue'
import BeginnerCover from '@/assets/edp/beginner.jpg'
import IntermediateCover from '@/assets/edp/intermediate.jpg'
import AdvancedCover from '@/assets/edp/advanced.jpg'

/** ----- INTERFACE ----- */

/** ----- SETUP ----- */
const route = useRoute()
const router = useRouter()
const message = useMessage()
const positionId = ref<number>(Number(route.query.positionId) || 0)
const positionList = ref<PostVO[]>([]) // 岗位列表
const phaseLearningMap = ref<PhaseLearningMap>() // 存储学习地图数据
const levels = ref([
  { key: PhaseEnum.PHASE1, label: 'Beginner' },
  { key: PhaseEnum.PHASE2, label: 'Intermediate' },
  { key: PhaseEnum.PHASE3, label: 'Advanced' }
])
const phases = ref([
  { key: PhaseEnum.PHASE1, label: 'Phase Ⅰ' },
  { key: PhaseEnum.PHASE2, label: 'Phase II' },
  { key: PhaseEnum.PHASE3, label: 'Phase III' }
])
const descriptions = ref([
  { key: PhaseEnum.PHASE1, label: 'Getting started and building the basics' },
  { key: PhaseEnum.PHASE2, label: 'In-depth learning and skills development' },
  { key: PhaseEnum.PHASE3, label: 'Comprehensive application & capacity improvement' }
])

// 一级技能选项列表
const firstLevelSkills = ref([
  { key: FirstLevelSkillEnum.SOFT_SKILL, label: 'Soft Skill' },
  { key: FirstLevelSkillEnum.HARD_SKILL, label: 'Hard Skill' },
  { key: FirstLevelSkillEnum.HSE, label: 'HSE' }
])

/** ----- FUNCTIONS ----- */
/** 获取岗位列表 */
const getPositionList = async () => {
  try {
    const res = await getPositionListForCurrentUser()

    positionList.value = res
    console.log('获取岗位列表成功:', positionList.value)
  } catch (error) {
    console.log('获取岗位列表失败:', error)
  }

  // 只有在直接访问页面（没有positionId参数）且用户有多个岗位时才跳转到选择岗位页面
  if (!positionId.value && positionList.value.length > 1) {
    const position = positionList.value[0]
    if (position && position.id && position.name) {
      toCareerDevelopMap()

      // 提示用户
      message.success('You have more than one position, redirecting to Career Develop Map...')
    }
  }
}

/** 获取岗位学习地图 */
const getLearningMap = async () => {
  try {
    // API调用
    phaseLearningMap.value = await LearningMapApi.getLearningMapByPositionId(positionId.value)
    console.log('获取岗位学习地图成功:', phaseLearningMap.value)
  } catch (error) {
    console.log('岗位学习地图获取失败:', error)
  }
}

/** 跳转到职业发展路径页面 */
const toCareerDevelopMap = () => {
  router.push('/edp/career-develop-map')
}

/** 跳转到个人发展计划页面 */
const toStudyPlan = (phase?: number) => {
  router.push({
    path: '/edp/study-plan',
    query: { positionId: positionId.value, phase: phase }
  })
}

/** 获取一级技能名称 */
const getFirstLevelSkillLabel = (skillId: number): string => {
  const skill = firstLevelSkills.value.find((skill) => skill.key === Number(skillId))
  return skill ? skill.label : `Skill ${skillId}`
}

/** ----- LIFECYCLE HOOKS ----- */
onMounted(() => {
  getPositionList() // 组件挂载时获取岗位列表

  if (positionId.value) {
    getLearningMap() // 当存在岗位ID时获取学习地图
  }
})
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <div class="h-full flex flex-col">
        <div class="flex items-center justify-between px-5 py-3">
          <!--Header-->
          <div class="flex flex-col justify-start gap-1">
            <h2 class="text-xl font-semibold tracking-tight"> Learning Map </h2>
            <span class="text-sm text-muted-foreground">
              Three stages to help you become a job expert
            </span>
          </div>

          <!--创建个人发展计划按钮-->
          <Button @click="toStudyPlan" :disabled="!phaseLearningMap" class="cursor-pointer">
            <span> Create Personal Plan </span>
            <Icon name="ArrowRight" />
          </Button>
        </div>

        <!--分割线-->
        <Separator />

        <!-- 有数据时显示学习地图 -->
        <div
          v-if="phaseLearningMap && Object.keys(phaseLearningMap).length > 0"
          class="mt-10 grid grid-cols-1 md:grid-cols-3 w-full h-[80vh] flex-1 px-5"
        >
          <!-- 遍历所有阶段，根据index显示在不同的列 -->
          <template
            v-for="([phaseKey, learningMap], index) in Object.entries(phaseLearningMap)"
            :key="phaseKey"
          >
            <!-- 根据index决定列的样式 -->
            <div class="flex flex-col h-full">
              <!-- 根据index决定卡片的位置和内边距 -->
              <div
                class="mb-auto"
                :class="{
                  'pl-0 pr-10 mt-36': index === 0, // 第一列卡片靠下
                  'px-5 mt-16': index === 1, // 第二列卡片居中
                  'pl-10 pr-0 mt-0': index === 2 // 第三列卡片靠上
                }"
              >
                <Card>
                  <CardHeader class="p-0">
                    <AspectRatio :ratio="16 / 9">
                      <img
                        :src="
                          Number(phaseKey) === PhaseEnum.PHASE1
                            ? BeginnerCover
                            : Number(phaseKey) === PhaseEnum.PHASE2
                              ? IntermediateCover
                              : AdvancedCover
                        "
                        :alt="`${levels.find((_level) => _level.key === Number(phaseKey))?.label} Phase Cover`"
                        class="rounded-t-md object-cover w-full h-full"
                      />

                      <!--难度等级-->
                      <div class="absolute top-5 left-6">
                        <Badge variant="secondary" class="text-muted-foreground">
                          {{ levels.find((_level) => _level.key === Number(phaseKey))?.label }}
                        </Badge>
                      </div>
                    </AspectRatio>
                  </CardHeader>

                  <CardContent>
                    <div class="flex items-start justify-between mt-6">
                      <!--阶段信息-->
                      <div class="flex flex-col gap-1">
                        <!--阶段标题-->
                        <h3 class="text-xl font-medium">
                          {{ phases.find((_phase) => _phase.key === Number(phaseKey))?.label }}
                        </h3>

                        <!-- 阶段描述 -->
                        <p class="text-muted-foreground">
                          {{ descriptions.find((_desc) => _desc.key === Number(phaseKey))?.label }}
                        </p>

                        <!--学习内容数量-->
                        <div class="flex flex-row flex-wrap gap-2 mt-1">
                          <div
                            v-for="[numKey, numValue] in Object.entries(
                              learningMap.skillContentNumber
                            )"
                            :key="numKey"
                          >
                            <Badge variant="secondary" class="mt-1 text-muted-foreground">
                              {{ getFirstLevelSkillLabel(Number(numKey)) + `: ` + numValue }}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      <!--完成进度-->
                      <div>
                        <CircularProgress :progress="learningMap.progress" />
                      </div>
                    </div>

                    <!--课程示例-->
                    <template
                      v-if="learningMap.contentTitles && learningMap.contentTitles.length > 0"
                    >
                      <div class="my-5 min-h-8">
                        <div
                          v-for="content in learningMap.contentTitles"
                          :key="content"
                          class="flex flex-col items-start"
                        >
                          <Button
                            variant="link"
                            class="h-auto p-0 text-left w-full justify-start min-w-0 max-w-full"
                          >
                            <span class="truncate block w-full text-left">{{ content }}</span>
                          </Button>
                        </div>
                      </div>
                    </template>

                    <template v-else>
                      <div class="flex flex-col items-start my-5 min-h-8">
                        <span class="text-muted-foreground">No content for this phase</span>
                      </div>
                    </template>
                  </CardContent>
                </Card>
              </div>

              <!--底部阶梯-->
              <div class="mt-24 grow bg-primary" />
            </div>
          </template>
        </div>

        <!-- 空状态 - 居中显示 -->
        <div v-else class="flex items-center justify-center min-h-[60vh]">
          <EmptyPlaceholder />
        </div>
      </div>
    </template>
  </ContainerWrapper>
</template>
