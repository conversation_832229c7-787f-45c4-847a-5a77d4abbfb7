<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { CareerDevelopMapAPI, CareerDevelopMapRespVO } from '@/api/edp/careerdevelopmap'
import { useRouter } from 'vue-router'
import { Check, Circle, Dot } from 'lucide-vue-next'

/** ----- SETUP ----- */
const router = useRouter()
const careerPaths = ref<CareerDevelopMapRespVO[][]>([]) // 多条岗位路线图数据

/** ----- FUNCTIONS ----- */

/** 获得岗位路线图树 */
const getCareerDevelopMapTree = async () => {
  try {
    const data = await CareerDevelopMapAPI.getCareerDevelopMap()

    // 检查返回的数据是否只有一条并且没有子数据
    if (
      Array.isArray(data) &&
      data.length === 1 &&
      (!data[0].children || data[0].children.length === 0)
    ) {
      // 直接跳转到学习地图页面
      const singlePosition = data[0]
      toLearningMap(singlePosition.positionId, singlePosition.positionName)
      return
    }

    careerPaths.value = generateCareerPaths(data)
    console.log('岗位路线图数据成功😊:', careerPaths.value)
  } catch (error) {
    console.error('请求岗位路线图树时发生错误😫:', error)
  }
}

/** 生成多条职业发展路径 */
const generateCareerPaths = (data: CareerDevelopMapRespVO[]): CareerDevelopMapRespVO[][] => {
  // 如果数据不是数组或为空，返回空数组
  if (!Array.isArray(data) || data.length === 0) {
    return []
  }

  // 找到所有根节点
  const rootNodes = data.filter((item) => item.root === true)
  if (rootNodes.length === 0) return []

  const allPaths: CareerDevelopMapRespVO[][] = []

  // 为每个根节点生成路径
  rootNodes.forEach((rootNode) => {
    const paths = generatePathsFromNode(rootNode)
    allPaths.push(...paths)
  })

  return allPaths
}

/** 从指定节点生成所有可能的路径 */
const generatePathsFromNode = (node: CareerDevelopMapRespVO): CareerDevelopMapRespVO[][] => {
  // 如果没有子节点，返回只包含当前节点的路径
  if (!node.children || node.children.length === 0) {
    return [[node]]
  }

  const allPaths: CareerDevelopMapRespVO[][] = []

  // 为每个子节点递归生成路径
  node.children.forEach((child) => {
    const childPaths = generatePathsFromNode(child)
    // 将当前节点添加到每个子路径的开头
    childPaths.forEach((childPath) => {
      allPaths.push([node, ...childPath])
    })
  })

  return allPaths
}

/** 跳转到学习地图页面 */
const toLearningMap = (positionId: number, positionName: string) => {
  router.push({
    path: '/edp/learning-map',
    query: { positionId, positionName }
  })
}

/** ----- LIFECYCLE HOOK ----- */
onMounted(() => {
  getCareerDevelopMapTree() // 获取岗位路线图树数据
})
</script>

<template>
  <ContainerWrap title="Career Development Map">
    <!-- 多条职业发展路径 -->
    <div v-if="careerPaths.length > 0" class="space-y-8 py-8">
      <div v-for="(careerPath, pathIndex) in careerPaths" :key="`path-${pathIndex}`" class="w-full">
        <!-- 路径标题 -->
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-foreground"> Career Path {{ pathIndex + 1 }} </h3>
          <p class="text-sm text-muted-foreground">
            {{ careerPath[0]?.sectName || 'Department' }} →
            {{ careerPath[careerPath.length - 1]?.sectName || 'Target Position' }}
          </p>
        </div>

        <!-- 单条路径的 Stepper -->
        <Stepper class="flex w-full items-start justify-start gap-0 min-h-[200px]">
          <StepperItem
            v-for="(position, index) in careerPath"
            :key="position.id"
            v-slot="{ state }"
            class="relative flex flex-1 flex-col items-start justify-start"
            :step="index + 1"
          >
            <StepperSeparator
              v-if="index < careerPath.length - 1"
              class="absolute left-[20px] right-[calc(-100%+20px)] top-[20px] block h-0.5 shrink-0 rounded-full bg-muted group-data-[state=completed]:bg-primary"
            />

            <div class="flex flex-col items-start w-full">
              <StepperTrigger as-child>
                <Button
                  :variant="state === 'completed' || state === 'active' ? 'default' : 'outline'"
                  size="icon"
                  class="z-10 rounded-full shrink-0 cursor-pointer mb-5"
                  :class="[
                    state === 'active' && 'ring-2 ring-ring ring-offset-2 ring-offset-background'
                  ]"
                  :disabled="index !== 0"
                  @click="toLearningMap(position.positionId, position.positionName)"
                >
                  <Check v-if="state === 'completed'" class="size-5" />
                  <Circle v-if="state === 'active'" />
                  <Dot v-if="state === 'inactive'" />
                </Button>
              </StepperTrigger>

              <div class="flex flex-col items-start text-left w-full">
                <StepperTitle
                  :class="[state === 'active' && 'text-primary']"
                  class="text-sm font-semibold transition lg:text-base"
                >
                  {{ position.positionName }}
                </StepperTitle>
                <StepperDescription
                  :class="[state === 'active' && 'text-primary']"
                  class="items-start sr-only text-xs text-muted-foreground transition md:not-sr-only lg:text-sm"
                >
                  {{ position.sectName || 'No Department Name' }}
                </StepperDescription>
              </div>
            </div>
          </StepperItem>
        </Stepper>
      </div>
    </div>

    <!-- 空状态 - 居中显示 -->
    <div v-else class="flex items-center justify-center min-h-[80vh]">
      <EmptyPlaceholder />
    </div>
  </ContainerWrap>
</template>
