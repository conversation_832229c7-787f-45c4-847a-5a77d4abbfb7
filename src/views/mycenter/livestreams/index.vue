<template>
  <ContainerScroll>
    <!-- Header with Status Filter -->
    <template #header>
      <div class="px-6 py-3 w-full h-full flex items-center">
        <div class="flex items-center justify-between w-full">
          <TabNav
            :tabs="[{ key: 'all', label: 'All My Live Streams' }]"
            :active-tab="'all'"
          />

          <!-- Status Filter in Top Right -->
          <ToggleGroup v-model="selectedStatus" type="single" size="sm">
            <ToggleGroupItem value="all" class="text-xs">All</ToggleGroupItem>
            <ToggleGroupItem value="upcoming" class="text-xs">Upcoming</ToggleGroupItem>
            <ToggleGroupItem value="live" class="text-xs">Live</ToggleGroupItem>
            <ToggleGroupItem value="ended" class="text-xs">Ended</ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>
    </template>

    <!-- Scrollable Content -->
    <div class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="p-6">
          <LiveStreamGrid
            :livestream-list="livestreamList"
            :loading="loading"
            @livestream-click="handleLiveStreamClick"
          />
        </div>
      </ScrollArea>
    </div>

    <!-- Fixed Pagination at Bottom -->
    <template #footer v-if="total > 0">
      <div class="px-6 py-4 w-full h-full flex items-center">
        <SmartPagination
          class="w-full"
          :total="total"
          :current-page="queryParams.pageNo"
          :page-size="queryParams.pageSize"
          @current-change="handlePageChange"
        />
      </div>
    </template>
  </ContainerScroll>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { ContainerScroll } from '@/components/ContainerWrap'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { SmartPagination } from '@/components/SmartPagination'
import { ScrollArea } from '@/components/ui/scroll-area'
import LiveStreamGrid from './components/LiveStreamGrid.vue'
import { LiveStreamApi, LiveRoomRespVO, LiveStatusEnum } from '@/api/mycenter/myrecords/livestream'
import { useUserStore } from '@/store/modules/user'
import TabNav from '@/components/TabNav.vue'

const router = useRouter()
const userStore = useUserStore()

// Reactive state
const selectedStatus = ref('all')
const loading = ref(false)
const livestreamList = ref<LiveRoomRespVO[]>([])
const total = ref(0)

// Query parameters for API calls
const queryParams = reactive({
  name: '',
  statusList: '',
  startTime: '',
  sortRule: 1,
  userId: '',
  pageNo: 1,
  pageSize: 12
})

// Status filter mapping
const getStatusListFromFilter = (filter: string): string => {
  switch (filter) {
    case 'upcoming':
      return `${LiveStatusEnum.TO_BE_RELEASED},${LiveStatusEnum.TO_BE_BROADCAST}`
    case 'live':
      return `${LiveStatusEnum.LIVE_BROADCAST}`
    case 'ended':
      return `${LiveStatusEnum.CLOSED}`
    case 'all':
    default:
      return ''
  }
}

// API call to fetch live stream list
const getLiveStreamList = async () => {
  try {
    loading.value = true

    // Clean up empty parameters
    const params = { ...queryParams }
    if (!params.name) {
      delete params.name
    }
    if (!params.startTime) {
      delete params.startTime
    }

    // Set status filter
    const statusList = getStatusListFromFilter(selectedStatus.value)
    if (statusList) {
      params.statusList = statusList
    } else {
      delete params.statusList
    }

    // Set current user ID
    params.userId = userStore.user?.id?.toString() || ''

    console.log('Live Stream API params:', params)

    const response = await LiveStreamApi.getMyLiveRoomPage(params)
    console.log('Live Stream API response:', response)

    livestreamList.value = response.list || []
    total.value = response.total || 0
  } catch (error) {
    console.error('Failed to fetch live stream list:', error)
    livestreamList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// Event handlers
const handlePageChange = (page: number) => {
  queryParams.pageNo = page
  getLiveStreamList()
}

const handleLiveStreamClick = (livestream: LiveRoomRespVO) => {
  console.log('Live stream clicked:', livestream)
  // Navigate to live stream detail page
  if (livestream.id) {
    router.push(`/live/detail?id=${livestream.id}`)
    // router.push({ name: 'LiveDetail', params: { id: livestream.id } })
  }
}

// Watch for status changes
watch(selectedStatus, (newStatus, oldStatus) => {
  console.log('Live stream status changed:', oldStatus, '→', newStatus)

  // Skip initialization
  if (oldStatus === undefined) return

  // Reset to first page when status changes
  queryParams.pageNo = 1
  getLiveStreamList()
})

// Initialize data on component mount
onMounted(() => {
  console.log('My Live Streams component mounted')
  getLiveStreamList()
})

// Refresh data when component is activated (for keep-alive)
onActivated(() => {
  getLiveStreamList()
})

// Emit pagination updates to parent component
const emit = defineEmits<{
  'update-pagination': [data: { total: number; currentPage: number; pageSize: number }]
}>()

// Pagination data for UI
const paginationData = computed(() => ({
  total: total.value,
  currentPage: queryParams.pageNo,
  pageSize: queryParams.pageSize
}))

// Watch pagination data and emit updates
watch(
  paginationData,
  (newData) => {
    emit('update-pagination', newData)
  },
  { immediate: true }
)
</script>
