<template>
  <div class="bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden group cursor-pointer" @click="handleClick">
    <div class="flex items-center p-4 gap-4">
      <!-- Policy Image/Icon -->
      <div class="relative w-24 h-24 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg overflow-hidden flex-shrink-0">
        <img
          v-if="policy.cover"
          :src="policy.cover"
          :alt="policy.title"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div v-else class="w-full h-full flex items-center justify-center text-white">
          <FileText class="w-8 h-8" />
        </div>

        <!-- Policy Type Badge -->
        <div v-if="policy.type" class="absolute top-1 right-1">
          <Badge
            variant="outline"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ getPolicyTypeLabel(policy.type) }}
          </Badge>
        </div>
      </div>

      <!-- Policy Content -->
      <div class="flex-1 min-w-0">
        <!-- Policy Title -->
        <h3 class="font-semibold text-slate-900 text-base mb-1 group-hover:text-green-600 transition-colors line-clamp-1">
          {{ policy.title }}
        </h3>

        <!-- Policy Department -->
        <div class="flex items-center gap-4 text-sm mb-2">
          <span v-if="policy.departmentName" class="text-green-600 font-medium">
            {{ policy.departmentName }}
          </span>
          <span v-if="policy.ack" class="text-slate-500 text-xs">
            Acknowledged
          </span>
        </div>

        <!-- Policy Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-2">
          <div v-if="policy.createTime" class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDate(policy.createTime) }}</span>
          </div>
          <div v-if="policy.updateTime" class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>Updated {{ formatDate(policy.updateTime) }}</span>
          </div>
          <div v-if="policy.fileTypeList?.length" class="flex items-center gap-1">
            <FileText class="w-3 h-3" />
            <span>{{ policy.fileTypeList.join(', ') }}</span>
          </div>
        </div>

        <!-- Policy Declaration -->
        <div v-if="policy.declaration" class="text-sm text-slate-600 line-clamp-1">
          {{ policy.declaration }}
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          @click.stop="emit('click', policy)"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <ExternalLink class="w-4 h-4 mr-1" />
          View
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Clock,
  Calendar,
  Building,
  ExternalLink,
  FileText
} from 'lucide-vue-next'

interface Props {
  policy: any
}

interface Emits {
  (e: 'click', policy: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Get status label based on API specification
const getStatusLabel = (status: any) => {
  const statusMap = {
    0: 'Not Started',
    1: 'In Progress',
    3: 'Completed'
  }
  return statusMap[status as keyof typeof statusMap] || 'Unknown'
}

// Get status variant
const getStatusVariant = (status: any) => {
  const variantMap = {
    0: 'secondary',     // Not Started - gray
    1: 'default',       // In Progress - blue
    3: 'outline'        // Completed - green outline
  }
  return variantMap[status as keyof typeof variantMap] || 'secondary'
}

// Get policy type label
const getPolicyTypeLabel = (type: any) => {
  const typeMap = {
    1: 'Company',
    2: 'Department',
    3: 'Project',
    4: 'General'
  }
  return typeMap[type as keyof typeof typeMap] || 'Policy'
}

// Format date
const formatDate = (date: string | number) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

// Handle click
const handleClick = () => {
  emit('click', props.policy)
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
