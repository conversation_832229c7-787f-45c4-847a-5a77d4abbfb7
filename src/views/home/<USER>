<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  getMyTaskNum,
  getStatistics,
  TodoCoursesVO,
  getTodoExams,
  getTodoCertificates,
  getTodoTrainings,
  TodoCertificatesVO,
  getTodoCompanyPolicies,
  TodoCompanyPoliciesVO,
  getTodoJourneys,
  TodoJourneysVO,
  getTodoLives,
  TodoLivesVO,
  getTodoSurveys,
  TodoSurveysVO,
  MyTaskNumRespVO,
  HomeRespVO,
  getBannerCarousels,
  TodoTrainingsVO,
  liveStatusEnum
} from '@/api/home'
import { secondsToHHmmss } from '@/utils/date'
import { useUserStore } from '@/store/modules/user'
import Autoplay from 'embla-carousel-autoplay'
import { ILinkItem } from '@/components/LinkNav/src/types'
import { UserStatus } from '@/utils/authControl'
import { getTodoCourses } from '@/api/home'
import { StudyStatusEnum, StudyStatusLabels } from '@/api/learning/course'
import {
  BookOpen,
  FileText,
  Shield,
  GraduationCap,
  Map,
  Briefcase,
  ListTodo,
  LucideIcon,
  Clock,
  Search,
  User,
  CheckCircle,
  ChevronRight,
  Award
} from 'lucide-vue-next'
import { transform } from 'typescript'

/** ----- INTERFACE ----- */
interface TaskNumber extends MyTaskNumRespVO {
  total: number
}

interface Todo extends ILinkItem {
  component: string
  badge: number
  icon: LucideIcon
  color: string
  userStatus: UserStatus[]
}

/** SETUP */
const plugin = Autoplay({
  delay: 2000,
  stopOnMouseEnter: true,
  stopOnInteraction: false
})
const carouselItems = ref()
const loading = ref(false)
const taskLoading = ref(false)
const router = useRouter()
const userStore = useUserStore()
const totalPendingCourse = ref(0)
const totalPendingExam = ref(0)
const statisticsInfo = ref<HomeRespVO>({
  learningDuration: 0
})
// 定义 TODO List 分次请求存储变量
const mandatoryCourses = ref<TodoCoursesVO[]>([])
const electiveCourses = ref<TodoCoursesVO[]>([])
const exams = ref<any[]>([])
const companyPolicies = ref<TodoCompanyPoliciesVO[]>([])
const journeys = ref<TodoJourneysVO[]>([])
const lives = ref<TodoLivesVO[]>([])
const surveys = ref<TodoSurveysVO[]>([])
const certificates = ref<TodoCertificatesVO[]>([])
const trainings = ref<TodoTrainingsVO[]>([])
// 定义 TODO List 总数
const mandatoryCoursesTotal = ref(0)
const electiveCoursesTotal = ref(0)
const examsTotal = ref(0)
const companyPoliciesTotal = ref(0)
const journeysTotal = ref(0)
const livesTotal = ref(0)
const surveysTotal = ref(0)
const certificatesTotal = ref(0)
const trainingsTotal = ref(0)
const emit = defineEmits(['getTotalBadge', 'getTotalCourse', 'getTotalExam'])
const myTaskNum = ref<TaskNumber>()
const todoList = reactive<Todo[]>([])
const cardColors = [
  '#0079b4', // 蓝色
  '#6b9e48', // 绿色
  '#c6a84d', // 黄色
  '#b6bf5e', // 黄绿色
  '#9eafa2' // 灰绿色
] // 卡片颜色系统

/** ----- FUNCTIONS ----- */
/** 获取轮播 */
const getListCarousels = async () => {
  try {
    loading.value = true
    const data = await getBannerCarousels({
      pageNum: 1,
      pageSize: 4,
      status: 1
    })
    carouselItems.value = data.list.sort((a, b) => a.sort - b.sort)
  } catch {
  } finally {
    loading.value = false
  }
}

/** 获取任务数量 */
const getTaskNumber = () => {
  getMyTaskNum().then((res) => {
    myTaskNum.value = res
    if (myTaskNum.value) {
      todoList[0].badge = myTaskNum.value.requiredCourseNum
      todoList[1].badge = myTaskNum.value.examNum
      todoList[2].badge = myTaskNum.value.onboardingNum
      todoList[3].badge = myTaskNum.value.companyPolicyNum
      todoList[4].badge = myTaskNum.value.electiveNum
      // 学习地图
      todoList[5].badge = myTaskNum.value.journeyNum

      // 暂时排除 onboarding 计算总数
      myTaskNum.value.total =
        myTaskNum.value?.companyPolicyNum +
        myTaskNum.value?.electiveNum +
        myTaskNum.value?.examNum +
        // myTaskNum.value?.onboardingNum + // 暂时注释
        myTaskNum.value?.requiredCourseNum +
        myTaskNum.value?.journeyNum
      /** 菜单全部展示 */
      console.log('myTaskNum.value.total====>', myTaskNum.value.total)
      emit('getTotalBadge', myTaskNum.value.total)
      emit('getTotalCourse', myTaskNum.value?.electiveNum + myTaskNum.value?.requiredCourseNum)
      emit('getTotalExam', myTaskNum.value.examNum)
    }
  })
}

/** 逐个获取待办列表 */
const getMandatoryCourses = async () => {
  try {
    const res = await getTodoCourses({
      pageNo: 1,
      pageSize: 3,
      type: 1
    })
    // 确保 res 是数组，如果是对象则取 list 属性
    mandatoryCourses.value = res.list
    mandatoryCoursesTotal.value = res.total
    console.log('MandatoryCourses====>', mandatoryCourses.value)
  } catch (error) {
    console.error('Failed to fetch mandatory courses:', error)
    mandatoryCourses.value = []
  }
} // 获取必修课待办列表
const getElectiveCourses = async () => {
  try {
    const res = await getTodoCourses({
      pageNo: 1,
      pageSize: 3,
      type: 0
    })
    // 确保 res 是数组，如果是对象则取 list 属性
    electiveCourses.value = res.list
    electiveCoursesTotal.value = res.total
    console.log('ElectiveCourses====>', electiveCourses.value)
  } catch (error) {
    console.error('Failed to fetch elective courses:', error)
    electiveCourses.value = []
  }
} // 获取选修课待办列表
const getExams = async () => {
  try {
    const res = await getTodoExams({
      pageNo: 1,
      pageSize: 3,
      type: 0
    })
    exams.value = res.list
    examsTotal.value = res.total
    console.log('Exams====>', exams.value)
  } catch (error) {
    console.error('Failed to fetch exams:', error)
    exams.value = []
  }
} // 获取考试待办列表
const getCompanyPolicies = async () => {
  try {
    const res = await getTodoCompanyPolicies({
      pageNo: 1,
      pageSize: 3
    })
    companyPolicies.value = res.list
    companyPoliciesTotal.value = res.total
    console.log('CompanyPolicies====>', companyPolicies.value)
  } catch (error) {
    console.error('Failed to fetch company policies:', error)
    companyPolicies.value = []
  }
} // 获取公司政策待办列表
const getJourneys = async () => {
  try {
    const res = await getTodoJourneys({
      pageNo: 1,
      pageSize: 3
    })
    journeys.value = res.list
    journeysTotal.value = res.total
    console.log('Journeys====>', journeys.value)
  } catch (error) {
    console.error('Failed to fetch journeys:', error)
    journeys.value = []
  }
} // 获取学习地图待办列表
const getLives = async () => {
  try {
    const res = await getTodoLives({
      pageNo: 1,
      pageSize: 3,
      statusList: [liveStatusEnum.NOT_STARTED, liveStatusEnum.LIVING]
    })
    lives.value = res.list
    livesTotal.value = res.total
    console.log('Lives====>', lives.value)
  } catch (error) {
    console.error('Failed to fetch lives:', error)
    lives.value = []
  }
} // 获取直播待办列表
const getSurveys = async () => {
  try {
    const res = await getTodoSurveys({
      pageNo: 1,
      pageSize: 3,
    })
    surveys.value = res.list
    surveysTotal.value = res.total
    console.log('Surveys====>', surveys.value)
  } catch (error) {
    console.error('Failed to fetch surveys:', error)
    surveys.value = []
  }
}
// 获取证书待办列表
const getCertificates = async () => {
  try {
    const res = await getTodoCertificates({
      pageNo: 1,
      pageSize: 3
    })
    certificates.value = res.list
    certificatesTotal.value = res.total
    console.log('Certificates====>', certificates.value)
  } catch (error) {
    console.error('Failed to fetch certificates:', error)
    certificates.value = []
  }
}
const getTrainings = async () => {
  try {
    const res = await getTodoTrainings({
      pageNo: 1,
      pageSize: 3
    })
    trainings.value = res.list
    trainingsTotal.value = res.total
    console.log('Trainings====>', trainings.value)
  } catch (error) {
    console.error('Failed to fetch trainings:', error)
    trainings.value = []
  }
} // 获取MLC Training待办列表

/** 获取卡片颜色和样式 */
const getCardColorStyle = (index: number) => {
  const color = cardColors[index % cardColors.length]
  return {
    backgroundColor: `${color}1A`, // 10% 透明度背景
    iconBackground: `${color}33`, // 20% 透明度图标背景
    iconColor: color,
    borderColor: `${color}4D` // 30% 透明度边框
  }
}

/** 获取数据 */
function getStatisticsInfo() {
  getStatistics().then((res) => {
    statisticsInfo.value = res
  })
}

/** 计算完成率 */
const getCompletionRate = () => {
  if (!myTaskNum.value || myTaskNum.value.total === 0) {
    return 100 // 如果没有任务，显示100%
  }

  // 这里可以根据实际业务逻辑计算完成率
  // 目前显示一个基于学习时长的估算值
  const totalTasks = myTaskNum.value.total
  const learningHours = statisticsInfo.value.learningDuration / 3600

  // 简单的完成率估算：每小时学习完成1个任务
  const estimatedCompletion = Math.min(Math.round((learningHours / totalTasks) * 100), 100)

  return estimatedCompletion || 0
}

/** 处理跳转News */
function handleToNews(item: any) {
  router.push({
    name: 'NewsDetail',
    params: {
      newsId: item.id
    }
  })
}

// 获取学习状态的徽章样式
const getStudyStatusVariant = (status: number) => {
  const variants = {
    [StudyStatusEnum.PENDING]: 'secondary', // PENDING - 未开始
    [StudyStatusEnum.IN_PROGRESS]: 'default', // IN_PROGRESS - 进行中
    [StudyStatusEnum.COMPLETED]: 'default' // COMPLETED - 已完成
  }
  return variants[status] || 'secondary'
}

// 获取学习状态的标签文本
const getStudyStatusText = (status: number) => {
  return StudyStatusLabels[status as StudyStatusEnum] || 'Unknown'
}

// 格式化时间为 mm-dd-yyyy 格式
const formatDate = (time: string | number) => {
  if (!time) return ''

  const date = new Date(time)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const year = date.getFullYear()

  return `${month}-${day}-${year}`
}
// 格式化时间将为2025-07-03 转换为 mm-dd-yyyy 格式
const convertDateFormat = (dateStr: string | number) => {
  return dateStr.replace(/(\d{4})-(\d{2})-(\d{2})/, '$2-$3-$1')
}

// 格式化时间为 hh:mm:ss 格式
const formatTime = (time: string | number) => {
  if (!time) return ''

  const date = new Date(time)
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${hours}:${minutes}:${seconds}`
}

// 导航到任务详情
const navigateToTask = (task: any, categoryType: string) => {
  console.log('Navigate to task:', task, categoryType)

  // 根据任务类型和具体任务导航
  const taskRoutes = {
    courses: {
      // 课程任务跳转到课程详情页
      path: '/content',
      params: { id: task.id }
    },
    exams: {
      // 考试任务跳转到考试详情页
      path: '/exam',
      params: { id: task.id }
    },
    policies: {
      // 政策任务跳转到政策详情页
      path: '/policy',
      params: { id: task.id }
    },
    onboarding: {
      // 入职任务跳转到入职流程页
      path: '/onboarding',
      query: { taskId: task.id }
    },
    journeys: {
      // 学习路径跳转到路径详情
      path: '/journey',
      params: { id: task.id }
    },
    training: {
      // 培训任务跳转到培训详情
      path: '/training',
      params: { id: task.id }
    },
    lives: {
      // 直播跳转到直播详情页
      path: '/live',
      params: { id: task.id }
    },
    surveys: {
      // 调查问卷跳转到问卷详情页
      path: '/survey',
      params: { id: task.id }
    },
    certificates: {
      // 证书跳转到证书详情页
      path: '/certificate',
      params: { id: task.id }
    }
  }

  const routeConfig = taskRoutes[categoryType]
  if (routeConfig) {
    const routeParams: any = { path: routeConfig.path }

    if (routeConfig.params) {
      routeParams.params = routeConfig.params
    }
    if (routeConfig.query) {
      routeParams.query = routeConfig.query
    }

    router.push(routeParams)
  }
}

// 导航到分类页面
const navigateToCategory = (categoryType: string) => {
  console.log('Navigate to category:', categoryType)

  // 映射到 my-center 对应的 tab 和状态
  const myCenterRoutes = {
    courses: {
      path: '/my-center',
      query: {
        tab: 'courses',
        courseType: 'mandatory', // 默认显示必修课
        status: 'in-progress'
      }
    },
    exams: {
      path: '/my-center',
      query: {
        tab: 'exams',
        status: 'pending'
      }
    },
    policies: {
      path: '/my-center',
      query: {
        tab: 'policy',
        status: 'pending'
      }
    },
    journey: {
      path: '/my-center',
      query: {
        tab: 'records', // 学习记录中查看 journey
        type: 'journey',
        status: 'in-progress'
      }
    },
    training: {
      path: '/my-center',
      query: {
        tab: 'records',
        type: 'training',
        status: 'in-progress'
      }
    }
  }

  const routeConfig = myCenterRoutes[categoryType]
  if (routeConfig) {
    router.push({
      path: routeConfig.path,
      query: routeConfig.query
    })
  } else {
    // 备用方案：直接跳转到对应页面
    const fallbackRoutes = {
      courses: '/content',
      exams: '/exam',
      policies: '/policy',
      onboarding: '/onboarding',
      journey: '/journey',
      training: '/training'
    }

    const fallbackRoute = fallbackRoutes[categoryType]
    if (fallbackRoute) {
      router.push(fallbackRoute)
    }
  }
}

onMounted(async () => {
  taskLoading.value = true
  try {
    await Promise.all([
      getTaskNumber(),
      getStatisticsInfo(),
      getListCarousels(),
      getMandatoryCourses(),
      getElectiveCourses(),
      getExams(),
      getCompanyPolicies(),
      getJourneys(),
      getLives(),
      getSurveys(),
      getCertificates(),
      getTrainings()
    ])
  } finally {
    taskLoading.value = false
  }
})
</script>

<template>
  <!-- 顶部 Banner -->
  <div class="mb-4 grid grid-cols-1 lg:grid-cols-3 gap-4">
    <!-- 轮播图 -->
    <div class="lg:col-span-2">
      <div class="rounded-lg overflow-hidden bg-gray-50 h-[350px]">
        <Carousel
          class="relative h-full"
          :plugins="[plugin]"
          :opts="{ loop: true, transition: 5 }"
          @mouseenter="plugin.stop"
          @mouseleave="[plugin.reset(), plugin.play(), console.log('Running')]"
        >
          <CarouselContent class="h-full">
            <CarouselItem
              v-for="item in carouselItems"
              :key="item.id"
              class="cursor-pointer h-full"
              @click="handleToNews(item)"
            >
              <div class="w-full h-full">
                <img :src="`${item.image}`" class="h-full bg-cover" alt="Banner image" />
              </div>
            </CarouselItem>
          </CarouselContent>
          <CarouselPrevious
            class="absolute z-10 -translate-y-1/2 left-4 top-1/2"
            @mouseenter="plugin.stop"
          />
          <CarouselNext
            class="absolute z-10 -translate-y-1/2 right-4 top-1/2"
            @mouseenter="plugin.stop"
          />
        </Carousel>
      </div>
    </div>

    <!-- 个人信息 & 统计数据 -->
    <div class="lg:col-span-1">
      <!-- 用户信息卡片 - 暂时注释 -->
      <!-- <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <div class="flex items-center gap-4">
          <div class="w-16 h-16 rounded-full overflow-hidden bg-white shadow-sm">
            <img
              class="w-full h-full object-cover"
              src="@/assets/images/user.png"
              alt="User avatar"
            />
          </div>
          <div>
            <h3 class="font-semibold text-gray-900 text-lg">
              {{ userStore.user.nickname }}
            </h3>
            <p class="text-sm text-gray-500">Learning Dashboard</p>
          </div>
        </div>
      </div> -->

      <!-- 统计数据容器 - 与轮播保持相同高度 -->
      <div class="bg-gray-50 rounded-lg p-4 h-[350px] flex flex-col">
        <!-- 主要统计数据 -->
        <div class="grid grid-cols-3 gap-3 mb-4">
          <!-- 学习时长 -->
          <div
            class="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-3 flex flex-col items-center text-center shadow-sm hover:shadow-md transition-shadow"
          >
            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mb-2">
              <Clock class="w-5 h-5 text-white" />
            </div>
            <div class="text-sm font-bold text-blue-900 mb-1">
              {{ secondsToHHmmss(statisticsInfo.learningDuration) }}
            </div>
            <div class="text-xs text-blue-700 font-medium">Learning Duration</div>
          </div>

          <!-- 总待办任务 -->
          <div
            class="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-3 flex flex-col items-center text-center shadow-sm hover:shadow-md transition-shadow"
          >
            <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center mb-2">
              <ListTodo class="w-5 h-5 text-white" />
            </div>
            <div class="text-sm font-bold text-purple-900 mb-1">
              {{ myTaskNum?.total || 0 }}
            </div>
            <div class="text-xs text-purple-700 font-medium">Total Tasks</div>
          </div>

          <!-- 完成进度 -->
          <div
            class="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-3 flex flex-col items-center text-center shadow-sm hover:shadow-md transition-shadow"
          >
            <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mb-2">
              <CheckCircle class="w-5 h-5 text-white" />
            </div>
            <div class="text-sm font-bold text-green-900 mb-1"> {{ getCompletionRate() }}% </div>
            <div class="text-xs text-green-700 font-medium">Completion Rate</div>
          </div>
        </div>

        <!-- 详细任务统计 -->
        <div class="flex-1 grid grid-cols-2 gap-2">
          <!-- 必修课程 -->
          <div
            class="bg-white border border-gray-200 rounded-lg p-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <div
              class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0"
            >
              <BookOpen class="w-4 h-4 text-red-600" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-semibold text-gray-900">{{
                myTaskNum?.requiredCourseNum || 0
              }}</div>
              <div class="text-xs text-gray-600">Required</div>
            </div>
          </div>

          <!-- 选修课程 -->
          <div
            class="bg-white border border-gray-200 rounded-lg p-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <div
              class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0"
            >
              <BookOpen class="w-4 h-4 text-blue-600" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-semibold text-gray-900">{{
                myTaskNum?.electiveNum || 0
              }}</div>
              <div class="text-xs text-gray-600">Elective</div>
            </div>
          </div>

          <!-- 考试 -->
          <div
            class="bg-white border border-gray-200 rounded-lg p-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <div
              class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0"
            >
              <FileText class="w-4 h-4 text-orange-600" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-semibold text-gray-900">{{ myTaskNum?.examNum || 0 }}</div>
              <div class="text-xs text-gray-600">Exams</div>
            </div>
          </div>

          <!-- 入职培训 - 暂时注释 -->
          <!-- <div class="bg-white border border-gray-200 rounded-lg p-2 flex items-center gap-2 hover:bg-gray-50 transition-colors">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <GraduationCap class="w-4 h-4 text-green-600" />
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-sm font-semibold text-gray-900">{{ myTaskNum?.onboardingNum || 0 }}</div>
                <div class="text-xs text-gray-600">Onboarding</div>
              </div>
            </div> -->

          <!-- 公司政策 -->
          <div
            class="bg-white border border-gray-200 rounded-lg p-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <div
              class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0"
            >
              <Shield class="w-4 h-4 text-purple-600" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-semibold text-gray-900">{{
                myTaskNum?.companyPolicyNum || 0
              }}</div>
              <div class="text-xs text-gray-600">Policies</div>
            </div>
          </div>

          <!-- 学习路径 -->
          <div
            class="bg-white border border-gray-200 rounded-lg p-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <div
              class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0"
            >
              <Map class="w-4 h-4 text-indigo-600" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-semibold text-gray-900">{{
                myTaskNum?.journeyNum || 0
              }}</div>
              <div class="text-xs text-gray-600">Journeys</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 快捷操作 -->
  <div class="bg-white rounded-lg border shadow p-4 mb-4">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
      <Button variant="outline" class="h-12 justify-start" @click="$router.push('/content')">
        <Search class="w-4 h-4 mr-2" />
        Browse Courses
      </Button>
      <Button variant="outline" class="h-12 justify-start" @click="$router.push('/exam')">
        <FileText class="w-4 h-4 mr-2" />
        Take Exam
      </Button>
      <Button variant="outline" class="h-12 justify-start" @click="$router.push('/my-center')">
        <User class="w-4 h-4 mr-2" />
        My Progress
      </Button>
      <Button variant="outline" class="h-12 justify-start" @click="$router.push('/journey')">
        <Map class="w-4 h-4 mr-2" />
        Learning Journey
      </Button>
    </div>
  </div>

  <!-- TODO 待办事项 -->
  <div class="space-y-4">
    <!-- 待办标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold tracking-tight">My To-Do Tasks</h2>
        <p class="text-muted-foreground">Your pending learning tasks and assignments</p>
      </div>
      <Badge variant="outline" class="text-sm"> 8 total tasks </Badge>
    </div>

    <!-- 待办卡片容器 - 栅格布局 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
      <!-- 必修课 -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(0).iconBackground }"
            >
              <BookOpen class="w-5 h-5" :style="{ color: getCardColorStyle(0).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Mandatory Courses</h3>
              <p class="text-xs text-muted-foreground">Required training courses</p>
            </div>
          </div>

          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>

          <!-- 子任务数 -->
          <div
            v-else-if="mandatoryCoursesTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(0).iconColor }"
          >
            {{ mandatoryCoursesTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="course in (Array.isArray(mandatoryCourses) ? mandatoryCourses : []).slice(
                0,
                3
              )"
              :key="course.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(course, 'courses')"
            >
              <div class="flex items-center justify-between">
                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ course.name }}</h4>
                  <p v-if="course.introduction" class="text-xs text-muted-foreground mt-1">{{
                    course.introduction
                  }}</p>
                </div>
                <ChevronRight class="w-4 h-4 text-gray-400 ml-2" />
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(mandatoryCourses) || mandatoryCourses.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-primary" />
              <p class="text-sm">All tasks completed!</p>
            </div>
          </template>
        </div>

        <!-- 查看全部按钮 -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('courses')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(mandatoryCourses) && mandatoryCourses.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ mandatoryCourses.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!-- 选修课 -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(1).iconBackground }"
            >
              <GraduationCap class="w-5 h-5" :style="{ color: getCardColorStyle(1).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Elective Courses</h3>
              <p class="text-xs text-muted-foreground">Optional skill enhancement</p>
            </div>
          </div>
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>
          <div
            v-else-if="electiveCoursesTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(1).iconColor }"
          >
            {{ electiveCoursesTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4 min-h-50">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`elective-skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <!-- 证书图片骨架屏 -->
                <Skeleton class="w-10 h-10 rounded-md mr-3" />

                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="course in (Array.isArray(electiveCourses) ? electiveCourses : []).slice(0, 3)"
              :key="course.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(course, 'courses')"
            >
              <div class="flex items-center justify-between">
                <!-- 证书封面 -->
                <img
                  v-if="course.cover"
                  :src="course.cover"
                  alt="Course Cover"
                  class="w-10 h-10 rounded-md mr-3"
                />

                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ course.name }}</h4>
                  <p v-if="course.introduction" class="text-xs text-muted-foreground mt-1">{{
                    course.introduction
                  }}</p>
                </div>
                <div class="ml-3 flex items-center gap-2">
                  <!-- 状态标签 -->
                  <Badge
                    v-if="course.studyStatus !== undefined && course.studyStatus !== null"
                    :variant="getStudyStatusVariant(course.studyStatus)"
                    class="text-xs"
                  >
                    {{ getStudyStatusText(course.studyStatus) }}
                  </Badge>
                  <!-- Arrow icon -->
                  <ChevronRight class="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(electiveCourses) || electiveCourses.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-green-500" />
              <p class="text-sm">All tasks completed!</p>
            </div>
          </template>
        </div>

        <!-- View All Button -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('elective')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(electiveCourses) && electiveCourses.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ electiveCourses.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!-- 考试 -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(3).iconBackground }"
            >
              <GraduationCap class="w-5 h-5" :style="{ color: getCardColorStyle(3).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Exams</h3>
              <p class="text-xs text-muted-foreground">Assesments to complete</p>
            </div>
          </div>
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>
          <div
            v-else-if="examsTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(3).iconColor }"
          >
            {{ examsTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4 min-h-50">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`elective-skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <!-- 封面骨架屏 -->
                <Skeleton class="w-10 h-10 rounded-md mr-3" />

                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="exam in (Array.isArray(exams) ? exams : []).slice(0, 3)"
              :key="exam.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(exam, 'exams')"
            >
              <div class="flex items-center justify-between">
                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ exam.name }}</h4>
                  <p
                    v-if="exam.beginTime && exam.endTime"
                    class="text-xs text-muted-foreground mt-1"
                  >
                    {{ exam.beginTime + ' - ' + exam.endTime }}</p
                  >
                </div>

                <div class="ml-3 flex items-center gap-2">
                  <!-- 状态标签 -->
                  <Badge variant="default" class="text-xs">
                    {{ exam.isPass ? 'Passed' : 'Failed' }}
                  </Badge>
                  <!-- Arrow icon -->
                  <ChevronRight class="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(exams) || exams.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-green-500" />
              <p class="text-sm">All tasks completed!</p>
            </div>
          </template>
        </div>

        <!-- View All Button -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('exams')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(exams) && exams.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ exams.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!-- 公司政策 -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(4).iconBackground }"
            >
              <GraduationCap class="w-5 h-5" :style="{ color: getCardColorStyle(4).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Company Policies</h3>
              <p class="text-xs text-muted-foreground">Policies to review</p>
            </div>
          </div>
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>
          <div
            v-else-if="companyPoliciesTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(4).iconColor }"
          >
            {{ companyPoliciesTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4 min-h-50">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`elective-skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <!-- 证书图片骨架屏 -->
                <Skeleton class="w-10 h-10 rounded-md mr-3" />

                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="policy in (Array.isArray(companyPolicies) ? companyPolicies : []).slice(0, 3)"
              :key="policy.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(policy, 'policies')"
            >
              <div class="flex items-center justify-between">
                <!-- 证书封面 -->
                <img
                  v-if="policy.cover"
                  :src="policy.cover"
                  alt="Policy Cover"
                  class="w-10 h-10 rounded-md mr-3"
                />

                <!-- 证书信息 -->
                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ policy.title }}</h4>
                  <p
                    v-if="policy.declaration"
                    class="text-xs text-muted-foreground mt-1 pr-2 truncate"
                  >
                    {{ policy.declaration }}
                  </p>
                </div>

                <!-- Arrow icon -->
                <ChevronRight class="w-4 h-4 text-gray-400" />
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(companyPolicies) || companyPolicies.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-green-500" />
              <p class="text-sm">No Policy</p>
            </div>
          </template>
        </div>

        <!-- View All Button -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('certificates')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(electiveCourses) && electiveCourses.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ electiveCourses.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!-- 学习地图 -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(5).iconBackground }"
            >
              <GraduationCap class="w-5 h-5" :style="{ color: getCardColorStyle(5).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Learning Journey</h3>
              <p class="text-xs text-muted-foreground">Personalized learning paths</p>
            </div>
          </div>
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>
          <div
            v-else-if="journeysTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(5).iconColor }"
          >
            {{ journeysTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4 min-h-50">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`elective-skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <!-- 证书图片骨架屏 -->
                <Skeleton class="w-10 h-10 rounded-md mr-3" />

                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="journey in (Array.isArray(journeys) ? journeys : []).slice(0, 3)"
              :key="journey.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(journey, 'journeys')"
            >
              <div class="flex items-center justify-between">
                <!-- 证书封面 -->
                <img
                  v-if="journey.cover"
                  :src="journey.cover"
                  alt="Policy Cover"
                  class="w-10 h-10 rounded-md mr-3"
                />

                <!-- 证书信息 -->
                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ journey.title }}</h4>
                  <p
                    v-if="journey.introduction"
                    class="text-xs text-muted-foreground mt-1 pr-2 truncate"
                  >
                    {{ journey.introduction }}
                  </p>
                </div>

                <!-- Arrow icon -->
                <ChevronRight class="w-4 h-4 text-gray-400" />
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(journeys) || journeys.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-green-500" />
              <p class="text-sm">No Journey</p>
            </div>
          </template>
        </div>

        <!-- View All Button -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('journeys')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(journeys) && journeys.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ journeys.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!-- 直播 -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(4).iconBackground }"
            >
              <GraduationCap class="w-5 h-5" :style="{ color: getCardColorStyle(4).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Live Streams</h3>
              <p class="text-xs text-muted-foreground">Scheduled sessions</p>
            </div>
          </div>
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>
          <div
            v-else-if="livesTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(4).iconColor }"
          >
            {{ livesTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4 min-h-50">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`elective-skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <!-- 证书图片骨架屏 -->
                <Skeleton class="w-10 h-10 rounded-md mr-3" />

                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="live in (Array.isArray(lives) ? lives : []).slice(0, 3)"
              :key="live.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(live, 'lives')"
            >
              <div class="flex items-center justify-between">
                <!-- 证书封面 -->
                <img
                  v-if="live.cover"
                  :src="live.cover"
                  alt="Live Cover"
                  class="w-10 h-10 rounded-md mr-3"
                />

                <!-- 证书信息 -->
                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ live.name }}</h4>
                  <p v-if="live.startTime" class="text-xs text-muted-foreground mt-1 pr-2 truncate">
                    {{ formatDate(live.startTime) }} {{ formatTime(live.startTime) }}
                  </p>
                </div>

                <!-- Arrow icon -->
                <ChevronRight class="w-4 h-4 text-gray-400" />
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(lives) || lives.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-green-500" />
              <p class="text-sm">No Lives</p>
            </div>
          </template>
        </div>

        <!-- View All Button -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('certificates')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(lives) && lives.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ lives.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!-- 调查问卷 -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(6).iconBackground }"
            >
              <GraduationCap class="w-5 h-5" :style="{ color: getCardColorStyle(6).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Surveys</h3>
              <p class="text-xs text-muted-foreground">Compete evaluations</p>
            </div>
          </div>
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>
          <div
            v-else-if="surveysTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(6).iconColor }"
          >
            {{ surveysTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4 min-h-50">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`elective-skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="survey in (Array.isArray(surveys) ? surveys : []).slice(0, 3)"
              :key="survey.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(survey, 'surveys')"
            >
              <div class="flex items-center justify-between">
                <!-- 证书信息 -->
                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ survey.name }}</h4>
                  <p
                    v-if="survey.description"
                    class="text-xs text-muted-foreground mt-1 pr-2 truncate"
                  >
                    {{ survey.description }}
                  </p>
                </div>

                <div class="ml-3 flex items-center gap-2">
                  <!-- 状态标签 -->
                  <Badge v-if="survey.statusName" variant="default" class="text-xs">
                    {{ survey.statusName }}
                  </Badge>
                  <!-- Arrow icon -->
                  <ChevronRight class="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(surveys) || surveys.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-green-500" />
              <p class="text-sm">No survey</p>
            </div>
          </template>
        </div>

        <!-- View All Button -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('certificates')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(surveys) && surveys.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ surveys.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!-- 证书 -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(8).iconBackground }"
            >
              <GraduationCap class="w-5 h-5" :style="{ color: getCardColorStyle(8).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Certificates</h3>
              <p class="text-xs text-muted-foreground">Personal Credentials</p>
            </div>
          </div>
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>
          <div
            v-else-if="certificatesTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(8).iconColor }"
          >
            {{ certificatesTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4 min-h-50">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`elective-skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <!-- 证书图片骨架屏 -->
                <Skeleton class="w-10 h-10 rounded-md mr-3" />

                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="certificate in (Array.isArray(certificates) ? certificates : []).slice(0, 3)"
              :key="certificate.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(certificate, 'certificates')"
            >
              <div class="flex items-center justify-between">
                <!-- 证书封面 -->
                <img
                  v-if="certificate.image"
                  :src="certificate.image"
                  alt="Certificate"
                  class="w-10 h-10 rounded-md mr-3"
                />

                <!-- 证书信息 -->
                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ certificate.name }}</h4>
                  <p v-if="certificate.expiresTime" class="text-xs text-muted-foreground mt-1">
                    Expires: {{ formatDate(certificate.expiresTime) }}
                  </p>
                </div>

                <!-- Arrow icon -->
                <ChevronRight class="w-4 h-4 text-gray-400" />
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(certificates) || certificates.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-green-500" />
              <p class="text-sm">No Certificate</p>
            </div>
          </template>
        </div>

        <!-- View All Button -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('certificates')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(electiveCourses) && electiveCourses.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ electiveCourses.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!-- MLC Training -->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- 卡片 Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :style="{ backgroundColor: getCardColorStyle(0).iconBackground }"
            >
              <GraduationCap class="w-5 h-5" :style="{ color: getCardColorStyle(0).iconColor }" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">MLC Training</h3>
              <p class="text-xs text-muted-foreground">Personal Trainings</p>
            </div>
          </div>
          <template v-if="taskLoading">
            <Skeleton class="h-6 w-6 rounded-full" />
          </template>
          <div
            v-else-if="trainingsTotal > 0"
            class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white"
            :style="{ backgroundColor: getCardColorStyle(8).iconColor }"
          >
            {{ trainingsTotal }}
          </div>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- 任务列表 -->
        <div class="space-y-3 flex-1 mb-4 min-h-50">
          <!-- 骨架屏 -->
          <template v-if="taskLoading">
            <div v-for="i in 3" :key="`elective-skeleton-${i}`" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between">
                <!-- 课程图片骨架屏 -->
                <Skeleton class="w-10 h-10 rounded-md mr-3" />

                <div class="flex-1 min-w-0 min-h-10">
                  <Skeleton class="h-4 w-3/4 mb-2" />
                  <Skeleton class="h-3 w-1/2" />
                </div>
                <Skeleton class="w-4 h-4 ml-2" />
              </div>
            </div>
          </template>

          <template v-else>
            <div
              v-for="training in (Array.isArray(trainings) ? trainings : []).slice(0, 3)"
              :key="training.id"
              class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              @click="navigateToTask(training, 'training')"
            >
              <div class="flex items-center justify-between">
                <!-- 封面 -->
                <img
                  v-if="training.classInfo?.courseCover"
                  :src="training.classInfo?.courseCover"
                  alt="Certificate"
                  class="w-10 h-10 rounded-md mr-3"
                />

                <!-- 课程信息 -->
                <div class="flex flex-col flex-1 min-w-0 min-h-10 justify-center">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{
                    training.classInfo.name
                  }}</h4>
                  <p
                    v-if="training.classInfo.startDate"
                    class="text-xs text-muted-foreground mt-1 pr-2 truncate"
                  >
                    {{ convertDateFormat(training.classInfo.startDate) }}
                    {{ training.classInfo.startTime }} ~ {{ training.classInfo.endTime }}
                  </p>
                  <!--                  <p v-if="training.expiresTime" class="text-xs text-muted-foreground mt-1">-->
                  <!--                    Expires: {{ formatDate(training.classInfo.startDate) }}-->
                  <!--                  </p>-->
                </div>

                <!-- Arrow icon -->
                <ChevronRight class="w-4 h-4 text-gray-400" />
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!Array.isArray(trainings) || trainings.length === 0"
              class="flex flex-col items-center justify-center text-center py-12 text-muted-foreground min-h-[200px]"
            >
              <CheckCircle class="w-8 h-8 mb-2 text-green-500" />
              <p class="text-sm">No Training</p>
            </div>
          </template>
        </div>

        <!-- View All Button -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory('training')"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <span
                v-if="Array.isArray(trainingsTotal) && trainingsTotal.length > 3"
                class="text-xs text-muted-foreground"
              >
                +{{ trainingsTotal.length - 3 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.usercard-number {
  @apply text-base font-bold;
}

.usercard-desc {
  @apply text-[#515151] text-xs;
}
</style>
