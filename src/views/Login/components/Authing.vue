<template>
  <div class="error-page" v-show="failureReminder" :loading="loginLoading">
    <div class="content-con">
      <img alt="" src="@/assets/imgs/error-401.svg" class="error-img" />
      <div class="text-con">
        <p class="text">401</p>
        <p class="text">System exception, please contact the administrator.</p>
      </div>
      <el-link :underline="false" type="primary" @click="jump" class="btn">Return to login</el-link>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { assignWith } from 'lodash-es'
import * as LoginApi from '@/api/login'
import * as authUtil from '@/utils/auth'
import { usePermissionStore } from '@/store/modules/permission'
import { encrypt } from '@/utils/jsencrypt'
import router from '@/router'
defineOptions({ name: 'Authing' })

const codeValue = ref('')
const failureReminder = ref(false)
const { t } = useI18n()
const { currentRoute, push } = useRouter()
const permissionStore = usePermissionStore()
const loginInfo = ref({})
const loading = ref() // ElLoading.service 返回的实例
const loginLoading = ref(false)
const redirect = ref<string>('')
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    tenantName: '芋道源码',
    username: 'admin',
    password: 'admin123',
    captchaVerification: '',
    rememberMe: true // 默认记录我。如果不需要，可手动修改
  }
})
const jump = async () => {
  await push({ name: 'Login' })
}
// 获取租户 ID
const getTenantId = async () => {
  if (loginData.tenantEnable === 'true') {
    const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
    authUtil.setTenantId(res)
  }
}
// 登录
const handleLogin = async (params) => {
  loginLoading.value = true
  try {
    await getTenantId()
    // const data = await validForm()
    // if (!data) {
    //   return
    // }
    // loginData.loginForm.captchaVerification = params.captchaVerification
    loginData.loginForm.password = encrypt(loginData.loginForm.password) as string
    loginInfo.value = await LoginApi.login(loginData.loginForm)
    if (!loginInfo.value) {
      return
    }
    authUtil.setToken(loginInfo.value)
    redirect.value = '/home'
    router.push({ path: redirect.value || permissionStore.addRouters[0].path }).then()
  } finally {
    loginLoading.value = false
  }
}
onMounted(async () => {
  const urlParams = new URLSearchParams(window.location.search)
  if (urlParams.has('code')) {
    codeValue.value = urlParams.get('code')
    if (codeValue.value) {
      await handleLogin({})
    }
  }
})
</script>
<style lang="scss" scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* 设置最小高度为视口高度 */
  background-color: #f5f7fa; /* 背景颜色可自定义 */
}

.content-con {
  .error-img {
    max-width: 80%; /* 限制图片最大宽度 */
    max-height: 350px; /* 限制图片最大高度 */
  }
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem; /* 内容之间的间距 */
}
.text-con {
  .text {
    font-size: 30px;
    font-weight: bold;
  }
  text-align: center;
}
.btn {
  font-size: 20px;
}
</style>
