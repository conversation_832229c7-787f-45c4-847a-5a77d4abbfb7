<template>
  <div class="bg-gradient-to-b from-[rgba(75,96,18,0.04)] to-[rgba(75,96,18,1)]">
    <!-- Header - 放在主容器外部 -->
    <div
      class="fixed top-0 left-0 right-0 flex flex-row items-center justify-between w-full px-6 py-4 z-50"
    >
      <div class="flex flex-row items-center gap-4">
        <img src="@/assets/imgs/logo.png" alt="logo" />
      </div>
      <div class="flex flex-row items-center gap-4">
        <I18n />
      </div>
    </div>

    <div class="flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
      <!-- 占位元素，防止内容被固定头部遮挡 -->
      <div class="h-5 w-full invisible"></div>

      <!--Body-->
      <div
        class="grid grid-cols-1 md:grid-cols-2 w-full max-w-[1440px] px-2 md:px-10 gap-0 md:gap-5 opacity-0 translate-y-3 motion-safe:animate-[fadeIn_0.8s_ease-out_forwards]"
      >
        <!--封面-->
        <div class="hidden md:flex items-center justify-center">
          <img :src="LoginCover" class="max-h-full w-full" />
        </div>

        <div class="flex flex-col gap-6 items-center justify-center">
          <Card class="bg-white/40 backdrop-blur-md border border-white/20 rounded-3xl">
            <CardHeader class="text-start p-8 !pb-0">
              <CardTitle class="text-xl"> Welcome to Online Learning Platform </CardTitle>
              <CardDescription> Login with your MFA or AD account </CardDescription>
            </CardHeader>
            <CardContent class="p-8">
              <div class="grid gap-6">
                <Tabs default-value="normal">
                  <TabsList class="grid w-full grid-cols-3">
                    <TabsTrigger value="normal"> Normal </TabsTrigger>
                    <TabsTrigger value="mfa"> MFA </TabsTrigger>
                    <TabsTrigger disabled value="ad"> AD </TabsTrigger>
                  </TabsList>
                  <TabsContent value="normal">
                    <form class="grid gap-6 mt-4" @submit.prevent="handleLogin">
                      <div class="grid gap-2">
                        <Label html-for="email">{{ t('login.username') }}</Label>
                        <Input
                          id="username"
                          type="text"
                          v-model="loginData.loginForm.username"
                          :placeholder="t('login.usernamePlaceholder')"
                          required
                        />
                      </div>
                      <div class="grid gap-2">
                        <div class="flex items-center">
                          <Label html-for="password">{{ t('login.password') }}</Label>
                          <a href="#" class="ml-auto text-sm underline-offset-4 hover:underline">
                            Forgot your password?
                          </a>
                        </div>
                        <Input
                          id="password"
                          type="password"
                          v-model="loginData.loginForm.password"
                          :placeholder="t('login.passwordPlaceholder')"
                          required
                        />
                      </div>
                      <Button type="submit" class="w-full h-10">
                        <Loader2 v-if="loginLoading" class="w-4 h-4 me-1 animate-spin" />
                        Login
                      </Button>
                    </form>
                    <div class="text-center text-sm mt-2">
                      Don't have an account?
                      <a href="#" class="underline underline-offset-4"> Sign up </a>
                    </div>
                  </TabsContent>
                  <TabsContent value="mfa">
                    <Button type="submit" class="w-full h-10" @click="mfaHandleLogin">
                      MFA Login
                    </Button>
                  </TabsContent>
                  <TabsContent value="ad">
                    <Card> </Card>
                  </TabsContent>
                </Tabs>

                <!--              <div class="flex flex-col gap-4">-->
                <!--                <Button variant="outline" class="w-full">-->
                <!--                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">-->
                <!--                    <path-->
                <!--                      d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701"-->
                <!--                      fill="currentColor"-->
                <!--                    />-->
                <!--                  </svg>-->
                <!--                  Login with MFA-->
                <!--                </Button>-->
                <!--                <Button variant="outline" class="w-full">-->
                <!--                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">-->
                <!--                    <path-->
                <!--                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"-->
                <!--                      fill="currentColor"-->
                <!--                    />-->
                <!--                  </svg>-->
                <!--                  Login with AD-->
                <!--                </Button>-->
                <!--              </div>-->
                <!--              <div-->
                <!--                class="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border"-->
                <!--              >-->
                <!--                <span class="relative z-10 bg-background px-2 text-muted-foreground">-->
                <!--                  Or continue with-->
                <!--                </span>-->
                <!--              </div>-->
              </div>
            </CardContent>
          </Card>
          <div
            class="text-balance text-center text-xs text-white/50 [&_a]:underline [&_a]:underline-offset-4 [&_a]:hover:text-primary"
          >
            By clicking continue, you agree to our <a href="#">Terms of Service</a> and
            <a href="#">Privacy Policy</a>.
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { GalleryVerticalEnd } from 'lucide-vue-next'
import { reactive } from 'vue'
import * as LoginApi from '@/api/login'
import * as authUtil from '@/utils/auth'
import router from '@/router'
import { useCache } from '@/hooks/web/useCache'
import { Loader2 } from 'lucide-vue-next'
import LoginCover from '@/assets/svgs/loginCover.png'
import { decrypt, encrypt } from '@/utils/jsencrypt'
import { config } from '@/config/axios/config'
import { loginTypeEnum } from '@/api/login'
import { useUserStore } from '@/store/modules/user'
// ===============

// ==================== 数据定义 ====================
const userStore = useUserStore()
const loginLoading = ref(false)
const formLogin = ref()
const { t } = useI18n()
const loginInfo = ref({})
const redirect = ref<string>('')

// 路由前缀
const { base_url } = config

/* 对象解构赋值 */
const { wsCache } = useCache()
const LoginRules = {
  tenantName: [required],
  username: [required],
  password: [required]
}
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    tenantName: import.meta.env.VITE_APP_DEFAULT_LOGIN_TENANT || '',
    username: import.meta.env.VITE_APP_DEFAULT_LOGIN_USERNAME || '',
    password: import.meta.env.VITE_APP_DEFAULT_LOGIN_PASSWORD || '',
    // password: 'admin123',
    captchaVerification: '',
    rememberMe: true // 默认记录我。如果不需要，可手动修改
  }
})

// ==================== 数据传递 ====================

// ==================== 方法定义 ====================
// 登录
const handleLogin = async (params) => {
  loginLoading.value = true
  userStore.setUserLoginType(loginTypeEnum.PASSWORD)
  try {
    await getTenantId()
    // const data = await validForm()
    // if (!data) {
    //   return
    // }
    // loginData.loginForm.captchaVerification = params.captchaVerification
    // 进行数据深拷贝, 防止登录密码在输入框展示被加密的字符串
    const loginDataLoginForm = JSON.parse(JSON.stringify(loginData.loginForm))
    loginDataLoginForm.password = encrypt(loginDataLoginForm.password) as string
    // 自定义登录
    loginInfo.value = await LoginApi.login(loginDataLoginForm)
    if (!loginInfo.value) {
      return
    }
    authUtil.setToken(loginInfo.value)
    redirect.value = '/home'
    router.push({ path: redirect.value || permissionStore.addRouters[0].path }).then()
  } finally {
    loginLoading.value = false
  }
}

// MFA登录
const mfaHandleLogin = () => {
  userStore.setUserLoginType(loginTypeEnum.MFA)
  window.location.href = `${base_url}/system/auth/authing/login`
}

// 获取租户 ID
const getTenantId = async () => {
  if (loginData.tenantEnable === 'true') {
    const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
    authUtil.setTenantId(res)
  }
}

// 根据域名，获得租户信息
const getTenantByWebsite = async () => {
  const website = location.host
  const res = await LoginApi.getTenantByWebsite(website)
  if (res) {
    loginData.loginForm.tenantName = res.name
    authUtil.setTenantId(res.id)
  }
}

// 记住密码
const getLoginFormCache = () => {
  const loginForm = authUtil.getLoginForm()
  if (loginForm) {
    loginData.loginForm = {
      ...loginData.loginForm,
      username: loginForm.username ? loginForm.username : loginData.loginForm.username,
      password: loginForm.password ? loginForm.password : loginData.loginForm.password,
      rememberMe: loginForm.rememberMe,
      tenantName: loginForm.tenantName ? loginForm.tenantName : loginData.loginForm.tenantName
    }
  }
}

// 挂载时加载
onMounted(() => {
  getLoginFormCache()
  getTenantByWebsite()
})
</script>

<style>
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0px);
  }
}

.animate-content-enter {
  opacity: 0;
  transform: translateY(12px);
  animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation-delay: 0.3s;
  will-change: transform, opacity;
  backface-visibility: hidden;
}
</style>
