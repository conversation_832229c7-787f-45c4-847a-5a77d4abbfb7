import request from '@/config/axios'
import { getRefreshToken } from '@/utils/auth'
import type { RegisterVO, UserLoginVO } from './types'

export interface SmsCodeVO {
  mobile: string
  scene: number
}

export interface SmsLoginVO {
  mobile: string
  code: string
}

// 登录方式 1.普通账号密码登录 2.AD登录(目前先定义，后续有AD登录需求直接使用) 3.MFA登录
export enum loginTypeEnum {
  PASSWORD = 1,
  AD = 2,
  MFA = 3
}

// 登录
export const login = (data: UserLoginVO) => {
  return request.appPost({ url: '/system/auth/login', data })
}

// 微软登录
export const microsoftLogin = (data: any) => {
  return request.post({ url: '/system/auth/microsoft/login', data })
}

// 注册
export const register = (data: RegisterVO) => {
  return request.post({ url: '/system/auth/register', data })
}

// MFA登录(前端回调接口通过code码取accessToken )
export const mfaLogin = (code: string) => {
  return request.appGet({ url: '/system/auth/login-by-code?code=' + code })
}

// 刷新访问令牌
export const refreshToken = () => {
  return request.post({ url: '/system/auth/refresh-token?refreshToken=' + getRefreshToken() })
}

// 使用租户名，获得租户编号
export const getTenantIdByName = (name: string) => {
  return request.get({ url: '/system/tenant/get-id-by-name?name=' + name })
}

// 使用租户域名，获得租户信息
export const getTenantByWebsite = (website: string) => {
  return request.get({ url: '/system/tenant/get-by-website?website=' + website })
}

// 登出(自定义和ad域登出)
export const loginOut = () => {
  return request.post({ url: '/system/auth/logout' })
}

// MFA登出
export const mfaLoginOut = () => {
  return request.appGet({ url: '/system/auth/authing/logout' })
}

// 获取用户权限信息
export const getInfo = (client: number) => {
  return request.get({ url: '/system/auth/get-permission-info?client=' + client })
}

//获取登录验证码
export const sendSmsCode = (data: SmsCodeVO) => {
  return request.post({ url: '/system/auth/send-sms-code', data })
}

// 短信验证码登录
export const smsLogin = (data: SmsLoginVO) => {
  return request.post({ url: '/system/auth/sms-login', data })
}

// 社交快捷登录，使用 code 授权码
export function socialLogin(type: string, code: string, state: string) {
  return request.post({
    url: '/system/auth/social-login',
    data: {
      type,
      code,
      state
    }
  })
}

// 社交授权的跳转
export const socialAuthRedirect = (type: number, redirectUri: string) => {
  return request.get({
    url: '/system/auth/social-auth-redirect?type=' + type + '&redirectUri=' + redirectUri
  })
}
// 获取验证图片以及 token
export const getCode = (data) => {
  return request.postOriginal({ url: 'system/captcha/get', data })
}

// 滑动或者点选验证
export const reqCheck = (data) => {
  return request.postOriginal({ url: 'system/captcha/check', data })
}
