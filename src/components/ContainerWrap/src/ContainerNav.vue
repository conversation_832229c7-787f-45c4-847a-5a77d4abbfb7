<script lang="ts" setup>
import { cn } from '@/lib/utils'
import { LinkProp } from '@/components/ContainerWrap'
import { MessageMyBizTypeRespVO, BizTypeEnum, SystemMessageTypeEnum } from '@/api/message'

interface NavProps {
  isCollapsed: boolean
  links: any[] // 支持不同的数据结构
  activeItem?: any // 当前激活的菜单项
}

interface BizTypeItem {
  title: string
  type: number
}
const { t } = useI18n() // 国际化

const bizTypes = ref<BizTypeItem[]>([
  {
    title: t('message.certificateNotification'),
    type: BizTypeEnum.CERTIFICATE_USER
  },
  {
    title: t('message.waitingListNotification'),
    type: BizTypeEnum.WAITING_LIST_PREFER_DATE
  },
  {
    title: t('message.classEndNotification'),
    type: BizTypeEnum.CLASS_END
  },
  {
    title: t('message.classCancelNotification'),
    type: BizTypeEnum.CLASS_CANCEL
  },
  {
    title: t('message.classPostponeNotification'),
    type: BizTypeEnum.CLASS_POSTPONE
  },
  {
    title: t('message.classPublishNotification'),
    type: BizTypeEnum.CLASS_PUBLISH
  },
  {
    title: t('message.taskApprovedNotification'),
    type: BizTypeEnum.LEANING_TASK_APPROVED
  },
  {
    title: t('message.taskRejectedNotification'),
    type: BizTypeEnum.LEANING_TASK_REJECTED
  }
])

// 后续如果有关于edp系统通知直接往后叠加,配置好枚举
const messageTypeNameMap = {
  [SystemMessageTypeEnum.ACADE_MY]: t('message.academy')
}

const selectedBizType = defineModel<string>('selectedBizType', { required: false })

const props = defineProps<NavProps>()
const route = useRoute()

const emit = defineEmits(['change-nav', 'changeNav'])

const selectedNav = ref<LinkProp | null>(null)

// 辅助函数：获取按钮变体
const getButtonVariant = (link: any) => {
  // My Center 页面使用 variant 属性
  if (link.variant) {
    return link.variant
  }
  // Message 页面使用 bizType 和 selectedBizType
  if (link.bizType !== undefined) {
    return selectedBizType.value === link.bizType ? 'default' : 'ghost'
  }
  // Live 页面：检查是否是当前激活的菜单项
  if (link.type !== undefined) {
    // 通过 activeItem prop 检查
    if (props.activeItem && props.activeItem.type === link.type) {
      return 'default'
    }
    // 通过路由参数检查
    const routeType = route.query.type as string
    if (routeType === link.type) {
      return 'default'
    }
    // 如果没有路由参数且是第一个菜单项，则激活
    if (!routeType && link.type === 'stream-central') {
      return 'default'
    }
  }
  return 'ghost'
}

// 辅助函数：获取按钮标题
const getButtonTitle = (link: any) => {
  // My Center 页面直接使用 title 属性
  if (link.title) {
    return link.title
  }
  // Live 页面使用 name 属性
  if (link.name) {
    return link.name
  }
  // Message 页面从 bizTypes 中查找
  if (link.bizType !== undefined) {
    return bizTypes.value.find((bizType) => bizType.type === link.bizType)?.title || ''
  }
  return ''
}

// 处理导航变化
const handleChangeNav = (linkOrBizType: any) => {
  // 如果是对象（My Center 页面），发送 key
  if (typeof linkOrBizType === 'object' && linkOrBizType.key) {
    emit('changeNav', { key: linkOrBizType.key })
  }
  // 如果是 bizType（Message 页面），发送 bizType
  else if (typeof linkOrBizType === 'object' && linkOrBizType.bizType) {
    selectedBizType.value = linkOrBizType.bizType
    emit('change-nav', selectedBizType.value)
  }
}
</script>

<template>
  <div
    :data-collapsed="isCollapsed"
    class="group flex flex-col gap-4 py-2 data-[collapsed=true]:py-2"
  >
    <nav
      class="grid gap-1 px-2 group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2"
    >
      <template v-for="(link, index) of links">
        <Tooltip v-if="isCollapsed" :key="`1-${index}`" :delay-duration="0">
          <TooltipTrigger as-child>
            <Button
              v-for="(bizLink, bizLinkIndex) in link"
              :key="bizLinkIndex"
              size="icon"
              :variant="getButtonVariant(bizLink)"
              :class="cn('h-9 w-9')"
              @click="handleChangeNav(bizLink)"
            >
              <component v-if="bizLink.icon" :is="bizLink.icon" class="size-4" />
              <span class="sr-only">{{ getButtonTitle(bizLink) }}</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right" class="flex items-center gap-4">
            {{ getButtonTitle(bizLink) }}
            <span v-if="bizLink.unreadCount || bizLink.count" class="ml-auto text-muted-foreground">
              {{ bizLink.unreadCount || bizLink.count }}
            </span>
          </TooltipContent>
        </Tooltip>

        <SidebarGroup v-else :key="index">
          <SidebarGroupLabel>{{ messageTypeNameMap[index] }}</SidebarGroupLabel>
          <SidebarGroupContent class="px-0">
            <SidebarMenu>
              <SidebarMenuItem v-for="(bizLink, bizLinkIndex) in link" :key="bizLinkIndex">
                <SidebarMenuButton
                  size="sm"
                  :variant="getButtonVariant(bizLink)"
                  :is-active="selectedBizType === bizLink.bizType"
                  :class="cn('justify-start')"
                  @click="handleChangeNav(bizLink)"
                >
                  <component v-if="bizLink.icon" :is="bizLink.icon" class="mr-2 size-4" />
                  {{ getButtonTitle(bizLink) }}
                  <span v-if="bizLink.unreadCount || bizLink.count" :class="cn('ml-auto')">
                    {{ bizLink.unreadCount || bizLink.count }}
                  </span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <!--        <Button-->
        <!--          v-else-->
        <!--          :key="`2-${index}`"-->
        <!--          size="sm"-->
        <!--          :variant="getButtonVariant(link)"-->
        <!--          :class="cn('justify-start')"-->
        <!--          @click="handleChangeNav(link)"-->
        <!--        >-->
        <!--          <component v-if="link.icon" :is="link.icon" class="mr-2 size-4" />-->
        <!--          {{ getButtonTitle(link) }}-->
        <!--          <span v-if="link.unreadCount || link.count" :class="cn('ml-auto')">-->
        <!--            {{ link.unreadCount || link.count }}-->
        <!--          </span>-->

        <!--        </Button>-->
      </template>
    </nav>
  </div>
</template>
